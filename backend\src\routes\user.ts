import { Router } from 'express'
import {
  getUsers,
  getUserById,
  createUser,
  updateUser,
  deleteUser,
  getUserStats,
  batchDeleteUsers,
  resetPassword,
  updateUserStatus
} from '../controllers/user'
import { authenticateToken } from '../middleware/auth'
import {
  requirePermission,
  requireRole,
  requireAnyRole
} from '../middleware/permission'
import { getUserIdParam, getPaginationParams } from '../utils/paramValidation'

/**
 * 用户管理路由
 * 提供用户的CRUD操作和管理功能
 */

const router = Router()

// ==================== 用户查询路由 ====================

/**
 * @route GET /api/users
 * @desc 获取用户列表（管理员）
 * @access 需要 user.list 权限
 * @query {number} [page=1] - 页码
 * @query {number} [limit=20] - 每页数量
 * @query {string} [search] - 搜索关键词（用户名或邮箱）
 * @query {string} [orderBy=createdAt] - 排序字段
 * @query {string} [orderDirection=DESC] - 排序方向
 * @query {boolean} [includeRoles=false] - 是否包含角色信息
 */
router.get('/',
  authenticateToken,
  requirePermission('user.list'),
  getUsers
)

/**
 * @route GET /api/users/stats
 * @desc 获取用户统计信息（管理员）
 * @access 需要 user.stats 权限
 */
router.get('/stats',
  authenticateToken,
  requirePermission('user.stats'),
  getUserStats
)

/**
 * @route GET /api/users/:id
 * @desc 根据ID获取用户详情（管理员）
 * @access 需要 user.read 权限
 * @param {number} id - 用户ID
 */
router.get('/:id',
  authenticateToken,
  requirePermission('user.read'),
  getUserById
)

// ==================== 用户管理路由 ====================

/**
 * @route POST /api/users
 * @desc 创建新用户（管理员）
 * @access 需要 user.create 权限
 * @body {string} username - 用户名（必填）
 * @body {string} email - 邮箱地址（必填）
 * @body {string} password - 密码（必填）
 * @body {number[]} [roleIds] - 角色ID数组（可选）
 */
router.post('/',
  authenticateToken,
  requirePermission('user.create'),
  createUser
)

/**
 * @route PUT /api/users/:id
 * @desc 更新用户信息（管理员）
 * @access 需要 user.update 权限
 * @param {number} id - 用户ID
 * @body {string} [username] - 用户名（可选）
 * @body {string} [email] - 邮箱地址（可选）
 * @body {string} [password] - 密码（可选）
 * @body {number[]} [roleIds] - 角色ID数组（可选）
 */
router.put('/:id',
  authenticateToken,
  requirePermission('user.update'),
  updateUser
)

/**
 * @route DELETE /api/users/:id
 * @desc 删除用户（超级管理员）
 * @access 需要 user.delete 权限
 * @param {number} id - 用户ID
 */
router.delete('/:id',
  authenticateToken,
  requirePermission('user.delete'),
  deleteUser
)

/**
 * @route POST /api/users/:id/reset-password
 * @desc 重置用户密码
 * @access 需要 user.update 权限
 * @param {number} id - 用户ID
 * @body {string} password - 新密码
 */
router.post('/:id/reset-password',
  authenticateToken,
  requirePermission('user.update'),
  resetPassword
)

/**
 * @route PATCH /api/users/:id/status
 * @desc 更新用户状态
 * @access 需要 user.update 权限
 * @param {number} id - 用户ID
 * @body {string} status - 用户状态 (active|inactive|banned)
 */
router.patch('/:id/status',
  authenticateToken,
  requirePermission('user.update'),
  updateUserStatus
)

// ==================== 用户角色管理路由 ====================

/**
 * @route GET /api/users/:id/roles
 * @desc 获取用户的角色列表
 * @access 需要 user.read 权限
 * @param {number} id - 用户ID
 */
router.get('/:id/roles',
  authenticateToken,
  requirePermission('user.read'),
  async (req, res, next) => {
    try {
      const userId = getUserIdParam(req)

      // 重定向到用户角色管理路由
      req.url = `/users/${userId}/roles`
      return next('route')
    } catch (error) {
      return next(error)
    }
  }
)

/**
 * @route POST /api/users/:id/roles
 * @desc 为用户分配角色
 * @access 需要 role.assign 权限
 * @param {number} id - 用户ID
 * @body {number[]} roleIds - 角色ID数组
 */
router.post('/:id/roles',
  authenticateToken,
  requirePermission('role.assign'),
  async (req, res, next) => {
    try {
      const userId = getUserIdParam(req)

      // 重定向到用户角色管理路由
      req.url = `/users/${userId}/roles/assign`
      return next('route')
    } catch (error) {
      return next(error)
    }
  }
)

// ==================== 批量操作路由 ====================

/**
 * @route POST /api/users/bulk/create
 * @desc 批量创建用户（超级管理员）
 * @access 需要 user.create 权限
 * @body {Array} users - 用户数据数组
 */
router.post('/bulk/create',
  authenticateToken,
  requireRole('admin'),
  async (req, res, next) => {
    try {
      // 这里可以实现批量创建用户的逻辑
      res.status(501).json({
        success: false,
        message: 'Bulk user creation not implemented yet'
      })
    } catch (error) {
      next(error)
    }
  }
)

/**
 * @route POST /api/users/bulk/delete
 * @desc 批量删除用户（超级管理员）
 * @access 需要 user.delete 权限
 * @body {number[]} userIds - 用户ID数组
 */
router.post('/bulk/delete',
  authenticateToken,
  requirePermission('user.delete'),
  batchDeleteUsers
)

/**
 * @route POST /api/users/bulk/assign-roles
 * @desc 批量分配角色（超级管理员）
 * @access 需要 role.assign 权限
 * @body {number[]} userIds - 用户ID数组
 * @body {number[]} roleIds - 角色ID数组
 */
router.post('/bulk/assign-roles',
  authenticateToken,
  requireRole('admin'),
  async (req, res, next) => {
    try {
      // 这里可以实现批量分配角色的逻辑
      res.status(501).json({
        success: false,
        message: 'Bulk role assignment not implemented yet'
      })
    } catch (error) {
      next(error)
    }
  }
)

// ==================== 用户搜索和筛选路由 ====================

/**
 * @route GET /api/users/search
 * @desc 搜索用户
 * @access 需要 user.list 权限
 * @query {string} q - 搜索关键词
 * @query {number} [limit=10] - 返回数量限制
 */
router.get('/search',
  authenticateToken,
  requirePermission('user.list'),
  async (req, res, next) => {
    try {
      const { q, limit = 10 } = req.query

      if (!q || typeof q !== 'string') {
        return res.status(400).json({
          success: false,
          message: 'Search query is required'
        })
      }

      // 重定向到主用户列表路由，带搜索参数
      req.query = { search: q, limit: String(limit), page: '1' }
      return getUsers(req, res, next)
    } catch (error) {
      return next(error)
    }
  }
)

/**
 * @route GET /api/users/by-role/:roleId
 * @desc 根据角色获取用户列表
 * @access 需要 user.list 权限
 * @param {number} roleId - 角色ID
 * @query {number} [page=1] - 页码
 * @query {number} [limit=20] - 每页数量
 */
router.get('/by-role/:roleId',
  authenticateToken,
  requirePermission('user.list'),
  async (req, res, next) => {
    try {
      const { limit: limitNum, offset: offsetNum } = getPaginationParams(req)
      const roleId = parseInt(req.params.roleId || '0')

      if (roleId <= 0) {
        return res.status(400).json({
          success: false,
          message: 'Invalid role ID'
        })
      }

      // 重定向到角色管理路由
      req.url = `/roles/${roleId}/users`
      return next('route')
    } catch (error) {
      return next(error)
    }
  }
)

export default router
