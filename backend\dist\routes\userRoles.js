"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const userRole_1 = require("../controllers/userRole");
const auth_1 = require("../middleware/auth");
const permission_1 = require("../middleware/permission");
const router = (0, express_1.Router)();
router.get('/me', auth_1.authenticateToken, userRole_1.getCurrentUserRoles);
router.get('/users/:userId/roles', auth_1.authenticateToken, (0, permission_1.requirePermission)('user.read'), userRole_1.getUserRoles);
router.get('/users/:userId/permissions', auth_1.authenticateToken, (0, permission_1.requirePermission)('user.read'), userRole_1.getUserPermissions);
router.put('/users/:userId/roles', auth_1.authenticateToken, (0, permission_1.requirePermission)('role.assign'), userRole_1.assignUserRoles);
router.post('/users/:userId/roles/:roleId', auth_1.authenticateToken, (0, permission_1.requirePermission)('role.assign'), userRole_1.addUserRole);
router.delete('/users/:userId/roles/:roleId', auth_1.authenticateToken, (0, permission_1.requirePermission)('role.assign'), userRole_1.removeUserRole);
router.get('/users/:userId/permissions/:permissionName/check', auth_1.authenticateToken, (0, permission_1.requirePermission)('user.read'), userRole_1.checkUserPermission);
router.post('/users/:userId/permissions/check-batch', auth_1.authenticateToken, (0, permission_1.requirePermission)('user.read'), userRole_1.checkUserPermissions);
router.get('/users/:userId/roles/:roleName/check', auth_1.authenticateToken, (0, permission_1.requirePermission)('user.read'), userRole_1.checkUserRole);
router.get('/roles/:roleId/users', auth_1.authenticateToken, (0, permission_1.requirePermission)('role.read'), userRole_1.getUsersByRole);
exports.default = router;
//# sourceMappingURL=userRoles.js.map