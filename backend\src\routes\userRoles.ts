import { Router } from 'express'
import {
  getUserRoles,
  getUserPermissions,
  assignUserRoles,
  addUserRole,
  removeUserRole,
  checkUserPermission,
  checkUserPermissions,
  checkUserRole,
  getUsersByRole,
  getCurrentUserRoles
} from '../controllers/userRole'
import { authenticateToken } from '../middleware/auth'
import {
  requirePermission,
  requireRole,
  requireAnyRole
} from '../middleware/permission'

/**
 * 用户角色管理路由
 * 提供用户角色分配和管理功能
 */

const router = Router()

/**
 * @route GET /api/user-roles/me
 * @desc 获取当前用户的角色和权限信息
 * @access 需要登录
 */
router.get('/me',
  authenticateToken,
  getCurrentUserRoles
)

/**
 * @route GET /api/user-roles/users/:userId/roles
 * @desc 获取用户的角色列表
 * @access 需要 user.read 权限
 */
router.get('/users/:userId/roles',
  authenticateToken,
  requirePermission('user.read'),
  getUserRoles
)

/**
 * @route GET /api/user-roles/users/:userId/permissions
 * @desc 获取用户的权限列表
 * @access 需要 user.read 权限
 */
router.get('/users/:userId/permissions',
  authenticateToken,
  requirePermission('user.read'),
  getUserPermissions
)

/**
 * @route PUT /api/user-roles/users/:userId/roles
 * @desc 为用户分配角色
 * @access 需要 role.assign 权限
 */
router.put('/users/:userId/roles',
  authenticateToken,
  requirePermission('role.assign'),
  assignUserRoles
)

/**
 * @route POST /api/user-roles/users/:userId/roles/:roleId
 * @desc 为用户添加单个角色
 * @access 需要 role.assign 权限
 */
router.post('/users/:userId/roles/:roleId',
  authenticateToken,
  requirePermission('role.assign'),
  addUserRole
)

/**
 * @route DELETE /api/user-roles/users/:userId/roles/:roleId
 * @desc 移除用户的单个角色
 * @access 需要 role.assign 权限
 */
router.delete('/users/:userId/roles/:roleId',
  authenticateToken,
  requirePermission('role.assign'),
  removeUserRole
)

/**
 * @route GET /api/user-roles/users/:userId/permissions/:permissionName/check
 * @desc 检查用户是否拥有指定权限
 * @access 需要 user.read 权限
 */
router.get('/users/:userId/permissions/:permissionName/check',
  authenticateToken,
  requirePermission('user.read'),
  checkUserPermission
)

/**
 * @route POST /api/user-roles/users/:userId/permissions/check-batch
 * @desc 批量检查用户权限
 * @access 需要 user.read 权限
 */
router.post('/users/:userId/permissions/check-batch',
  authenticateToken,
  requirePermission('user.read'),
  checkUserPermissions
)

/**
 * @route GET /api/user-roles/users/:userId/roles/:roleName/check
 * @desc 检查用户是否拥有指定角色
 * @access 需要 user.read 权限
 */
router.get('/users/:userId/roles/:roleName/check',
  authenticateToken,
  requirePermission('user.read'),
  checkUserRole
)

/**
 * @route GET /api/user-roles/roles/:roleId/users
 * @desc 获取拥有指定角色的用户列表
 * @access 需要 role.read 权限
 */
router.get('/roles/:roleId/users',
  authenticateToken,
  requirePermission('role.read'),
  getUsersByRole
)

export default router
