import { Response, NextFunction } from 'express';
import { AuthenticatedRequest } from './auth';
interface PermissionValidationConfig {
    permissions?: string[];
    roles?: string[];
    requireAll?: boolean;
    allowSelf?: boolean;
    resourceOwnerField?: string;
    skipIfOwner?: boolean;
}
export declare const validatePermissions: (config: PermissionValidationConfig) => (req: AuthenticatedRequest, res: Response, next: NextFunction) => Promise<void>;
export declare const PermissionValidators: {
    requireAdmin: () => (req: AuthenticatedRequest, res: Response, next: NextFunction) => Promise<void>;
    requireEditor: () => (req: AuthenticatedRequest, res: Response, next: NextFunction) => Promise<void>;
    requirePermission: (permission: string) => (req: AuthenticatedRequest, res: Response, next: NextFunction) => Promise<void>;
    requireAnyPermission: (permissions: string[]) => (req: AuthenticatedRequest, res: Response, next: NextFunction) => Promise<void>;
    requireAllPermissions: (permissions: string[]) => (req: AuthenticatedRequest, res: Response, next: NextFunction) => Promise<void>;
    requireOwnershipOrPermission: (permission: string) => (req: AuthenticatedRequest, res: Response, next: NextFunction) => Promise<void>;
    requireRoleOrOwnership: (roles: string[]) => (req: AuthenticatedRequest, res: Response, next: NextFunction) => Promise<void>;
};
declare class PermissionCache {
    private cache;
    private readonly TTL;
    get(key: string): boolean | null;
    set(key: string, result: boolean): void;
    clear(): void;
    clearUserCache(userId: number): void;
}
export declare const permissionCache: PermissionCache;
export declare const validatePermissionsWithCache: (config: PermissionValidationConfig) => (req: AuthenticatedRequest, res: Response, next: NextFunction) => Promise<void>;
export {};
//# sourceMappingURL=permissionValidator.d.ts.map