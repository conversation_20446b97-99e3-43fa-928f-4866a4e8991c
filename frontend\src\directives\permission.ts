/**
 * 权限指令
 * 用于控制页面元素的显示/隐藏和启用/禁用状态
 */

import type { App, DirectiveBinding } from 'vue'
import { useAuthStore } from '@/stores/auth'
import { useRbacStore } from '@/stores/rbac'
import type { Permission, Role } from '@/constants/permissions'

// 权限指令的值类型
interface PermissionDirectiveValue {
  permissions?: Permission | Permission[]
  roles?: Role | Role[]
  mode?: 'hide' | 'disable' | 'readonly'
  fallback?: 'hide' | 'disable' | 'readonly'
}

// 简化的权限指令值类型（直接传权限字符串）
type SimplePermissionValue = Permission | Permission[] | Role | Role[]

// 权限检查结果缓存
const permissionCache = new Map<string, boolean>()
const cacheTimeout = 5 * 60 * 1000 // 5分钟缓存

/**
 * 清除权限缓存
 */
export function clearPermissionCache() {
  permissionCache.clear()
}

/**
 * 检查用户权限
 * @param permissions 权限列表
 * @param roles 角色列表
 * @returns 是否有权限
 */
async function checkPermissions(
  permissions?: Permission | Permission[],
  roles?: Role | Role[]
): Promise<boolean> {
  const authStore = useAuthStore()
  const rbacStore = useRbacStore()

  if (!authStore.user) {
    return false
  }

  const userId = authStore.user.id
  const cacheKey = `${userId}-${JSON.stringify({ permissions, roles })}`

  // 检查缓存
  if (permissionCache.has(cacheKey)) {
    return permissionCache.get(cacheKey)!
  }

  try {
    let hasAccess = true

    // 检查角色权限
    if (roles) {
      const roleList = Array.isArray(roles) ? roles : [roles]
      const userRoles = await rbacStore.getUserRolesByUserId(userId)
      const userRoleNames = userRoles.map(role => role.name)

      hasAccess = roleList.some(role => userRoleNames.includes(role))
    }

    // 检查具体权限（只有在角色检查通过或没有角色要求时才检查）
    if (hasAccess && permissions) {
      const permissionList = Array.isArray(permissions) ? permissions : [permissions]

      // 使用批量检查优化性能
      if (permissionList.length > 1) {
        const permissionResults = await rbacStore.checkUserPermissions(userId, permissionList)
        hasAccess = Object.values(permissionResults).some(Boolean)
      } else {
        hasAccess = await rbacStore.checkUserPermission(userId, permissionList[0])
      }
    }

    // 缓存结果
    permissionCache.set(cacheKey, hasAccess)

    // 设置缓存过期
    setTimeout(() => {
      permissionCache.delete(cacheKey)
    }, cacheTimeout)

    return hasAccess
  } catch (error) {
    console.error('权限检查失败:', error)
    return false
  }
}

/**
 * 应用权限控制到元素
 * @param el DOM元素
 * @param hasPermission 是否有权限
 * @param mode 控制模式
 */
function applyPermissionControl(
  el: HTMLElement,
  hasPermission: boolean,
  mode: 'hide' | 'disable' | 'readonly' = 'hide'
) {
  if (hasPermission) {
    // 有权限时恢复元素状态
    el.style.display = ''
    el.removeAttribute('disabled')
    el.removeAttribute('readonly')
    el.classList.remove('permission-disabled', 'permission-readonly')
  } else {
    // 无权限时根据模式处理
    switch (mode) {
      case 'hide':
        el.style.display = 'none'
        break
      case 'disable':
        el.setAttribute('disabled', 'true')
        el.classList.add('permission-disabled')
        break
      case 'readonly':
        el.setAttribute('readonly', 'true')
        el.classList.add('permission-readonly')
        break
    }
  }
}

/**
 * 解析指令值
 * @param value 指令值
 * @returns 解析后的配置
 */
function parseDirectiveValue(
  value: PermissionDirectiveValue | SimplePermissionValue
): PermissionDirectiveValue {
  if (typeof value === 'string' || Array.isArray(value)) {
    // 简化形式：直接传权限或角色字符串
    const stringValue = Array.isArray(value) ? value : [value]

    // 判断是权限还是角色（简单的启发式判断）
    const isRole = stringValue.some(v =>
      ['super_admin', 'admin', 'editor', 'user'].includes(v)
    )

    return isRole
      ? { roles: value as Role | Role[] }
      : { permissions: value as Permission | Permission[] }
  }

  return value as PermissionDirectiveValue
}

/**
 * v-permission 指令
 * 用法：
 * v-permission="'user.create'" - 检查单个权限
 * v-permission="['user.create', 'user.update']" - 检查多个权限（任一即可）
 * v-permission="{ permissions: ['user.create'], mode: 'disable' }" - 详细配置
 * v-permission="{ roles: ['admin'], mode: 'hide' }" - 角色检查
 */
const permissionDirective = {
  async mounted(el: HTMLElement, binding: DirectiveBinding) {
    const config = parseDirectiveValue(binding.value)
    const hasPermission = await checkPermissions(config.permissions, config.roles)
    applyPermissionControl(el, hasPermission, config.mode || 'hide')
  },

  async updated(el: HTMLElement, binding: DirectiveBinding) {
    const config = parseDirectiveValue(binding.value)
    const hasPermission = await checkPermissions(config.permissions, config.roles)
    applyPermissionControl(el, hasPermission, config.mode || 'hide')
  }
}

/**
 * v-role 指令
 * 专门用于角色检查的简化指令
 * 用法：
 * v-role="'admin'" - 检查单个角色
 * v-role="['admin', 'editor']" - 检查多个角色（任一即可）
 */
const roleDirective = {
  async mounted(el: HTMLElement, binding: DirectiveBinding) {
    const roles = binding.value
    const hasPermission = await checkPermissions(undefined, roles)
    applyPermissionControl(el, hasPermission, 'hide')
  },

  async updated(el: HTMLElement, binding: DirectiveBinding) {
    const roles = binding.value
    const hasPermission = await checkPermissions(undefined, roles)
    applyPermissionControl(el, hasPermission, 'hide')
  }
}

/**
 * v-auth 指令
 * 检查用户是否已登录
 * 用法：
 * v-auth - 需要登录
 * v-auth="false" - 需要未登录
 */
const authDirective = {
  mounted(el: HTMLElement, binding: DirectiveBinding) {
    const authStore = useAuthStore()
    const requireAuth = binding.value !== false
    const isAuthenticated = authStore.isAuthenticated

    const hasAccess = requireAuth ? isAuthenticated : !isAuthenticated
    applyPermissionControl(el, hasAccess, 'hide')
  },

  updated(el: HTMLElement, binding: DirectiveBinding) {
    const authStore = useAuthStore()
    const requireAuth = binding.value !== false
    const isAuthenticated = authStore.isAuthenticated

    const hasAccess = requireAuth ? isAuthenticated : !isAuthenticated
    applyPermissionControl(el, hasAccess, 'hide')
  }
}

/**
 * 安装权限指令
 * @param app Vue应用实例
 */
export function setupPermissionDirectives(app: App) {
  app.directive('permission', permissionDirective)
  app.directive('role', roleDirective)
  app.directive('auth', authDirective)
}

// 导出指令
export {
  permissionDirective,
  roleDirective,
  authDirective
}

// 权限指令的CSS样式
export const permissionDirectiveStyles = `
.permission-disabled {
  opacity: 0.5;
  cursor: not-allowed;
  pointer-events: none;
}

.permission-readonly {
  opacity: 0.7;
  cursor: default;
}

.permission-disabled input,
.permission-disabled button,
.permission-disabled select,
.permission-disabled textarea {
  cursor: not-allowed;
}

.permission-readonly input,
.permission-readonly textarea {
  cursor: default;
  background-color: #f5f5f5;
}
`
