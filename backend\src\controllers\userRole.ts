import { Request, Response, NextFunction } from 'express'
import { User } from '../models/User'
import { Role } from '../models/Role'
import { UserRole } from '../models/UserRole'
import { createError } from '../middleware/errorHandler'
import { AuthenticatedRequest } from '../middleware/permission'
import { getUserIdParam, getRoleIdParam, getStringParam } from '../utils/paramValidation'

/**
 * 用户角色管理控制器
 * 提供用户角色分配和管理功能
 */

/**
 * 获取用户的角色列表
 * @param req - Express请求对象
 * @param res - Express响应对象
 * @param next - Express下一个中间件函数
 */
export const getUserRoles = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
  try {
    // 获取用户ID并验证
    const userIdNum = getUserIdParam(req)

    // 检查用户是否存在
    const user = await User.findByPk(userIdNum)
    if (!user) {
      throw createError(404, '用户不存在', 'USER_NOT_FOUND')
    }

    // 获取用户的角色
    const roles = await UserRole.getUserRoles(userIdNum)

    res.json({
      success: true,
      data: {
        userId: userIdNum,
        username: user.username,
        roles
      }
    })
  } catch (error) {
    next(error)
  }
}

/**
 * 获取用户的权限列表
 * @param req - Express请求对象
 * @param res - Express响应对象
 * @param next - Express下一个中间件函数
 */
export const getUserPermissions = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
  try {
    // 获取用户ID并验证
    const userIdNum = getUserIdParam(req)

    // 检查用户是否存在
    const user = await User.findByPk(userIdNum)
    if (!user) {
      throw createError(404, '用户不存在', 'USER_NOT_FOUND')
    }

    // 获取用户的权限
    const permissions = await UserRole.getUserPermissions(userIdNum)

    // 按资源分组权限
    const groupedPermissions: Record<string, any[]> = {}
    permissions.forEach(permission => {
      const resource = permission.resource
      if (!groupedPermissions[resource]) {
        groupedPermissions[resource] = []
      }
      groupedPermissions[resource].push(permission)
    })

    res.json({
      success: true,
      data: {
        userId: userIdNum,
        username: user.username,
        permissions,
        groupedPermissions
      }
    })
  } catch (error) {
    next(error)
  }
}

/**
 * 为用户分配角色
 * @param req - Express请求对象
 * @param res - Express响应对象
 * @param next - Express下一个中间件函数
 */
export const assignUserRoles = async (req: AuthenticatedRequest, res: Response, next: NextFunction): Promise<void> => {
  try {
    const { userId } = req.params
    const { roleIds } = req.body
    const assignedBy = req.user?.id

    // 检查用户是否存在
    const user = await User.findByPk(userId)
    if (!user) {
      throw createError(404, '用户不存在', 'USER_NOT_FOUND')
    }

    // 验证角色ID是否有效
    if (roleIds && roleIds.length > 0) {
      const validRoles = await Role.findAll({
        where: { id: roleIds, isActive: true }
      })

      if (validRoles.length !== roleIds.length) {
        throw createError(400, '包含无效的角色ID', 'INVALID_ROLE_IDS')
      }
    }

    // 分配角色
    const userIdNum = getUserIdParam(req)
    await UserRole.assignRoles(userIdNum, roleIds || [], assignedBy)

    // 获取更新后的角色列表
    const roles = await UserRole.getUserRoles(userIdNum)

    res.json({
      success: true,
      data: {
        userId: userIdNum,
        username: user.username,
        roles
      },
      message: '角色分配成功'
    })
  } catch (error) {
    next(error)
  }
}

/**
 * 为用户添加单个角色
 * @param req - Express请求对象
 * @param res - Express响应对象
 * @param next - Express下一个中间件函数
 */
export const addUserRole = async (req: AuthenticatedRequest, res: Response, next: NextFunction): Promise<void> => {
  try {
    const { userId, roleId } = req.params
    const assignedBy = req.user?.id

    // 检查用户是否存在
    const user = await User.findByPk(userId)
    if (!user) {
      throw createError(404, '用户不存在', 'USER_NOT_FOUND')
    }

    // 检查角色是否存在且激活
    const role = await Role.findOne({
      where: { id: roleId, isActive: true }
    })
    if (!role) {
      throw createError(404, '角色不存在或已禁用', 'ROLE_NOT_FOUND')
    }

    // 检查用户是否已经拥有该角色
    const userIdNum = getUserIdParam(req)
    const roleIdNum = getRoleIdParam(req)
    const hasRole = await UserRole.hasRole(userIdNum, roleIdNum)
    if (hasRole) {
      throw createError(400, '用户已经拥有该角色', 'USER_ALREADY_HAS_ROLE')
    }

    // 添加角色
    await UserRole.assignRole(userIdNum, roleIdNum, assignedBy)

    res.json({
      success: true,
      message: '角色添加成功'
    })
  } catch (error) {
    next(error)
  }
}

/**
 * 移除用户的单个角色
 * @param req - Express请求对象
 * @param res - Express响应对象
 * @param next - Express下一个中间件函数
 */
export const removeUserRole = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
  try {
    const { userId, roleId } = req.params

    // 检查用户是否存在
    const user = await User.findByPk(userId)
    if (!user) {
      throw createError(404, '用户不存在', 'USER_NOT_FOUND')
    }

    // 检查角色是否存在
    const role = await Role.findByPk(roleId)
    if (!role) {
      throw createError(404, '角色不存在', 'ROLE_NOT_FOUND')
    }

    // 检查用户是否拥有该角色
    const userIdNum = getUserIdParam(req)
    const roleIdNum = getRoleIdParam(req)
    const hasRole = await UserRole.hasRole(userIdNum, roleIdNum)
    if (!hasRole) {
      throw createError(400, '用户没有该角色', 'USER_DOES_NOT_HAVE_ROLE')
    }

    // 移除角色
    await UserRole.removeRole(userIdNum, roleIdNum)

    res.json({
      success: true,
      message: '角色移除成功'
    })
  } catch (error) {
    next(error)
  }
}

/**
 * 检查用户是否拥有指定权限
 * @param req - Express请求对象
 * @param res - Express响应对象
 * @param next - Express下一个中间件函数
 */
export const checkUserPermission = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
  try {
    const userIdNum = getUserIdParam(req)
    const permissionName = getStringParam(req, 'permissionName')

    // 检查用户是否存在
    const user = await User.findByPk(userIdNum)
    if (!user) {
      throw createError(404, '用户不存在', 'USER_NOT_FOUND')
    }
    const hasPermission = await UserRole.hasPermission(userIdNum, permissionName)

    res.json({
      success: true,
      data: {
        userId: userIdNum,
        username: user.username,
        permissionName,
        hasPermission
      }
    })
  } catch (error) {
    next(error)
  }
}

/**
 * 批量检查用户权限
 * @param req - Express请求对象
 * @param res - Express响应对象
 * @param next - Express下一个中间件函数
 */
export const checkUserPermissions = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
  try {
    const userIdNum = getUserIdParam(req)
    const { permissions } = req.body

    // 验证权限列表
    if (!Array.isArray(permissions) || permissions.length === 0) {
      throw createError(400, '权限列表不能为空', 'INVALID_PERMISSIONS')
    }

    // 检查用户是否存在
    const user = await User.findByPk(userIdNum)
    if (!user) {
      throw createError(404, '用户不存在', 'USER_NOT_FOUND')
    }

    // 批量检查权限
    const permissionResults: Record<string, boolean> = {}

    for (const permission of permissions) {
      if (typeof permission !== 'string') {
        throw createError(400, '权限名称必须是字符串', 'INVALID_PERMISSION_NAME')
      }

      try {
        const hasPermission = await UserRole.hasPermission(userIdNum, permission)
        permissionResults[permission] = hasPermission
      } catch (error) {
        console.error(`检查权限 ${permission} 失败:`, error)
        permissionResults[permission] = false
      }
    }

    res.json({
      success: true,
      data: {
        userId: userIdNum,
        username: user.username,
        permissions: permissionResults
      }
    })
  } catch (error) {
    next(error)
  }
}

/**
 * 检查用户是否拥有指定角色
 * @param req - Express请求对象
 * @param res - Express响应对象
 * @param next - Express下一个中间件函数
 */
export const checkUserRole = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
  try {
    const userIdNum = getUserIdParam(req)
    const roleName = getStringParam(req, 'roleName')

    // 检查用户是否存在
    const user = await User.findByPk(userIdNum)
    if (!user) {
      throw createError(404, '用户不存在', 'USER_NOT_FOUND')
    }
    const hasRole = await UserRole.hasRoleByName(userIdNum, roleName)

    res.json({
      success: true,
      data: {
        userId: userIdNum,
        username: user.username,
        roleName,
        hasRole
      }
    })
  } catch (error) {
    next(error)
  }
}

/**
 * 获取拥有指定角色的用户列表
 * @param req - Express请求对象
 * @param res - Express响应对象
 * @param next - Express下一个中间件函数
 */
export const getUsersByRole = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
  try {
    const { roleId } = req.params
    const { page = 1, limit = 10 } = req.query

    const pageNum = parseInt(page as string)
    const limitNum = parseInt(limit as string)
    const offset = (pageNum - 1) * limitNum

    // 检查角色是否存在
    const role = await Role.findByPk(roleId)
    if (!role) {
      throw createError(404, '角色不存在', 'ROLE_NOT_FOUND')
    }

    // 获取拥有该角色的用户
    const { rows: userRoles, count: total } = await UserRole.findAndCountAll({
      where: { roleId: getRoleIdParam(req) },
      limit: limitNum,
      offset,
      include: [
        {
          model: User,
          as: 'user',
          attributes: ['id', 'username', 'email', 'createdAt']
        },
        {
          model: User,
          as: 'assigner',
          attributes: ['id', 'username'],
          required: false
        }
      ],
      order: [['assignedAt', 'DESC']]
    })

    res.json({
      success: true,
      data: {
        role: {
          id: role.id,
          name: role.name,
          description: role.description
        },
        users: userRoles,
        pagination: {
          page: pageNum,
          limit: limitNum,
          total,
          pages: Math.ceil(total / limitNum)
        }
      }
    })
  } catch (error) {
    next(error)
  }
}

/**
 * 获取当前用户的角色和权限信息
 * @param req - Express请求对象
 * @param res - Express响应对象
 * @param next - Express下一个中间件函数
 */
export const getCurrentUserRoles = async (req: AuthenticatedRequest, res: Response, next: NextFunction): Promise<void> => {
  try {
    const userId = req.user?.id

    if (!userId) {
      throw createError(401, '需要登录', 'AUTHENTICATION_REQUIRED')
    }

    // 获取用户的角色和权限
    const roles = await UserRole.getUserRoles(userId)
    const permissions = await UserRole.getUserPermissions(userId)

    // 按资源分组权限
    const groupedPermissions: Record<string, any[]> = {}
    permissions.forEach(permission => {
      const resource = permission.resource
      if (!groupedPermissions[resource]) {
        groupedPermissions[resource] = []
      }
      groupedPermissions[resource].push(permission)
    })

    res.json({
      success: true,
      data: {
        userId,
        roles,
        permissions,
        groupedPermissions
      }
    })
  } catch (error) {
    next(error)
  }
}
