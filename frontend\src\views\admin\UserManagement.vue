<template>
  <div class="user-management">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <div class="header-left">
          <h1 class="page-title">
            <el-icon><User /></el-icon>
            用户管理
          </h1>
          <p class="page-description">管理系统用户、角色分配和权限控制</p>
        </div>
        <div class="header-actions">
          <el-button
            type="primary"
            :icon="Plus"
            @click="handleCreateUser"
            v-if="hasPermission('user.create')"
          >
            新建用户
          </el-button>
          <el-button
            :icon="Download"
            @click="handleExportUsers"
            v-if="hasPermission('user.export')"
          >
            导出用户
          </el-button>
          <el-button
            :icon="Upload"
            @click="handleImportUsers"
            v-if="hasPermission('user.import')"
          >
            导入用户
          </el-button>
        </div>
      </div>
    </div>

    <!-- 统计卡片 -->
    <div class="stats-section">
      <el-row :gutter="16">
        <el-col :xs="24" :sm="12" :md="6">
          <el-card class="stats-card">
            <div class="stats-content">
              <div class="stats-icon total">
                <el-icon><UserFilled /></el-icon>
              </div>
              <div class="stats-info">
                <div class="stats-value">{{ userStats?.totalUsers || 0 }}</div>
                <div class="stats-label">总用户数</div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :xs="24" :sm="12" :md="6">
          <el-card class="stats-card">
            <div class="stats-content">
              <div class="stats-icon active">
                <el-icon><Check /></el-icon>
              </div>
              <div class="stats-info">
                <div class="stats-value">{{ userStats?.activeUsers || 0 }}</div>
                <div class="stats-label">活跃用户</div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :xs="24" :sm="12" :md="6">
          <el-card class="stats-card">
            <div class="stats-content">
              <div class="stats-icon new">
                <el-icon><Plus /></el-icon>
              </div>
              <div class="stats-info">
                <div class="stats-value">{{ userStats?.newUsersToday || 0 }}</div>
                <div class="stats-label">今日新增</div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :xs="24" :sm="12" :md="6">
          <el-card class="stats-card">
            <div class="stats-content">
              <div class="stats-icon online">
                <el-icon><Connection /></el-icon>
              </div>
              <div class="stats-info">
                <div class="stats-value">{{ userStats?.onlineUsers || 0 }}</div>
                <div class="stats-label">在线用户</div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 筛选和搜索区域 -->
    <el-card class="filter-card" shadow="never">
      <div class="filter-content">
        <el-form :model="filterForm" inline class="filter-form">
          <el-form-item label="搜索">
            <el-input
              v-model="filterForm.search"
              placeholder="搜索用户名或邮箱"
              :prefix-icon="Search"
              clearable
              @input="handleSearch"
              style="width: 240px"
            />
          </el-form-item>

          <el-form-item label="状态">
            <el-select
              v-model="filterForm.status"
              placeholder="选择状态"
              clearable
              @change="handleFilter"
              style="width: 120px"
            >
              <el-option label="活跃" value="active" />
              <el-option label="禁用" value="inactive" />
              <el-option label="封禁" value="banned" />
            </el-select>
          </el-form-item>

          <el-form-item label="角色">
            <el-select
              v-model="filterForm.roleId"
              placeholder="选择角色"
              clearable
              @change="handleFilter"
              style="width: 140px"
            >
              <el-option
                v-for="role in roles"
                :key="role.id"
                :label="role.name"
                :value="role.id"
              />
            </el-select>
          </el-form-item>

          <el-form-item label="排序">
            <el-select
              v-model="filterForm.orderBy"
              @change="handleFilter"
              style="width: 120px"
            >
              <el-option label="创建时间" value="createdAt" />
              <el-option label="用户名" value="username" />
              <el-option label="邮箱" value="email" />
              <el-option label="最后登录" value="lastLoginAt" />
            </el-select>
          </el-form-item>

          <el-form-item>
            <el-select
              v-model="filterForm.orderDirection"
              @change="handleFilter"
              style="width: 80px"
            >
              <el-option label="降序" value="DESC" />
              <el-option label="升序" value="ASC" />
            </el-select>
          </el-form-item>

          <el-form-item>
            <el-button @click="handleResetFilter">重置</el-button>
          </el-form-item>
        </el-form>

        <!-- 批量操作 -->
        <div v-if="selectedUsers.length > 0" class="batch-actions">
          <span class="batch-info">已选择 {{ selectedUsers.length }} 个用户</span>
          <el-button
            type="danger"
            size="small"
            :icon="Delete"
            @click="handleBatchDelete"
            v-if="hasPermission('user.delete')"
          >
            批量删除
          </el-button>
          <el-button
            size="small"
            :icon="UserFilled"
            @click="handleBatchAssignRole"
            v-if="hasPermission('user.update')"
          >
            批量分配角色
          </el-button>
        </div>
      </div>
    </el-card>

    <!-- 用户列表 -->
    <el-card class="table-card" shadow="never">
      <div v-loading="loading" class="table-container">
        <el-table
          :data="users"
          @selection-change="handleSelectionChange"
          stripe
          class="user-table"
        >
          <el-table-column
            type="selection"
            width="55"
            v-if="hasPermission('user.delete') || hasPermission('user.update')"
          />

          <el-table-column label="用户信息" min-width="200">
            <template #default="{ row }">
              <div class="user-info">
                <el-avatar
                  :size="40"
                  :src="row.avatar"
                  class="user-avatar"
                >
                  {{ row.username.charAt(0).toUpperCase() }}
                </el-avatar>
                <div class="user-details">
                  <div class="user-name">
                    <el-button
                      type="text"
                      @click="handleViewUser(row)"
                      class="username-link"
                    >
                      {{ row.username }}
                    </el-button>
                    <el-tag
                      v-if="row.nickname"
                      size="small"
                      type="info"
                      class="nickname-tag"
                    >
                      {{ row.nickname }}
                    </el-tag>
                  </div>
                  <div class="user-email">{{ row.email }}</div>
                </div>
              </div>
            </template>
          </el-table-column>

          <el-table-column label="角色" width="200">
            <template #default="{ row }">
              <div class="roles-container">
                <el-tag
                  v-for="role in row.roles"
                  :key="role.id"
                  :type="getRoleTagType(role.name)"
                  size="small"
                  class="role-tag"
                >
                  {{ role.name }}
                </el-tag>
                <span v-if="!row.roles || row.roles.length === 0" class="no-roles">
                  暂无角色
                </span>
              </div>
            </template>
          </el-table-column>

          <el-table-column label="状态" width="100">
            <template #default="{ row }">
              <el-tag
                :type="getStatusTagType(row.status)"
                size="small"
              >
                {{ getStatusText(row.status) }}
              </el-tag>
            </template>
          </el-table-column>

          <el-table-column label="最后登录" width="160" show-overflow-tooltip>
            <template #default="{ row }">
              <div v-if="row.lastLoginAt" class="login-time">
                <el-icon><Clock /></el-icon>
                {{ formatDate(row.lastLoginAt) }}
              </div>
              <span v-else class="never-login">从未登录</span>
            </template>
          </el-table-column>

          <el-table-column label="创建时间" width="160" show-overflow-tooltip>
            <template #default="{ row }">
              <div class="create-time">
                <el-icon><Calendar /></el-icon>
                {{ formatDate(row.createdAt) }}
              </div>
            </template>
          </el-table-column>

          <el-table-column label="操作" width="200" fixed="right">
            <template #default="{ row }">
              <div class="action-buttons">
                <el-button
                  type="text"
                  size="small"
                  :icon="View"
                  @click="handleViewUser(row)"
                  v-if="hasPermission('user.read')"
                >
                  详情
                </el-button>
                <el-button
                  type="text"
                  size="small"
                  :icon="Edit"
                  @click="handleEditUser(row)"
                  v-if="hasPermission('user.update')"
                >
                  编辑
                </el-button>
                <el-dropdown
                  @command="(command) => handleUserAction(command, row)"
                  v-if="hasAnyUserAction()"
                >
                  <el-button type="text" size="small" :icon="More">
                    更多
                  </el-button>
                  <template #dropdown>
                    <el-dropdown-menu>
                      <el-dropdown-item
                        command="resetPassword"
                        v-if="hasPermission('user.update')"
                      >
                        <el-icon><Key /></el-icon>
                        重置密码
                      </el-dropdown-item>
                      <el-dropdown-item
                        :command="row.status === 'active' ? 'disable' : 'enable'"
                        v-if="hasPermission('user.update')"
                      >
                        <el-icon v-if="row.status === 'active'"><Lock /></el-icon>
                        <el-icon v-else><Unlock /></el-icon>
                        {{ row.status === 'active' ? '禁用用户' : '启用用户' }}
                      </el-dropdown-item>
                      <el-dropdown-item
                        command="forceLogout"
                        v-if="hasPermission('user.update')"
                      >
                        <el-icon><SwitchButton /></el-icon>
                        强制下线
                      </el-dropdown-item>
                      <el-dropdown-item
                        command="delete"
                        divided
                        v-if="hasPermission('user.delete')"
                      >
                        <el-icon><Delete /></el-icon>
                        删除用户
                      </el-dropdown-item>
                    </el-dropdown-menu>
                  </template>
                </el-dropdown>
              </div>
            </template>
          </el-table-column>
        </el-table>

        <!-- 空状态 -->
        <div v-if="!loading && users.length === 0" class="empty-state">
          <el-empty description="暂无用户数据">
            <el-button
              type="primary"
              :icon="Plus"
              @click="handleCreateUser"
              v-if="hasPermission('user.create')"
            >
              创建第一个用户
            </el-button>
          </el-empty>
        </div>
      </div>

      <!-- 分页 -->
      <div v-if="pagination.total > 0" class="pagination-container">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.limit"
          :total="pagination.total"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handlePageSizeChange"
          @current-change="handlePageChange"
        />
      </div>
    </el-card>

    <!-- 对话框 -->
    <!-- 用户详情对话框 -->
    <UserDetailDialog
      v-model="showDetailDialog"
      :user="currentUser"
      @edit="handleEditUser"
    />

    <!-- 用户编辑对话框 -->
    <UserEditDialog
      v-model="showEditDialog"
      :user="editingUser"
      :mode="editMode"
      @success="handleEditSuccess"
    />

    <!-- 删除确认对话框 -->
    <el-dialog
      v-model="showDeleteDialog"
      title="确认删除"
      width="400px"
      :before-close="handleDeleteCancel"
    >
      <div class="delete-content">
        <el-icon class="delete-icon"><WarningFilled /></el-icon>
        <div class="delete-text">
          <p>确定要删除用户 <strong>{{ deletingUser?.username }}</strong> 吗？</p>
          <p class="delete-warning">此操作不可撤销，用户的所有数据将被永久删除。</p>
        </div>
      </div>
      <template #footer>
        <el-button @click="handleDeleteCancel">取消</el-button>
        <el-button
          type="danger"
          :loading="deleteLoading"
          @click="handleDeleteConfirm"
        >
          确认删除
        </el-button>
      </template>
    </el-dialog>

    <!-- 批量删除确认对话框 -->
    <el-dialog
      v-model="showBatchDeleteDialog"
      title="批量删除确认"
      width="500px"
    >
      <div class="batch-delete-content">
        <el-icon class="delete-icon"><WarningFilled /></el-icon>
        <div class="delete-text">
          <p>确定要删除选中的 <strong>{{ selectedUsers.length }}</strong> 个用户吗？</p>
          <p class="delete-warning">此操作不可撤销，所有选中用户的数据将被永久删除。</p>
          <div class="selected-users">
            <el-tag
              v-for="user in selectedUsers.slice(0, 5)"
              :key="user.id"
              class="user-tag"
            >
              {{ user.username }}
            </el-tag>
            <span v-if="selectedUsers.length > 5" class="more-users">
              等 {{ selectedUsers.length }} 个用户
            </span>
          </div>
        </div>
      </div>
      <template #footer>
        <el-button @click="showBatchDeleteDialog = false">取消</el-button>
        <el-button
          type="danger"
          :loading="batchDeleteLoading"
          @click="handleBatchDeleteConfirm"
        >
          确认删除
        </el-button>
      </template>
    </el-dialog>

    <!-- 重置密码对话框 -->
    <el-dialog
      v-model="showResetPasswordDialog"
      title="重置密码"
      width="400px"
    >
      <el-form :model="resetPasswordForm" label-width="80px">
        <el-form-item label="用户">
          <el-input :value="resetPasswordUser?.username" disabled />
        </el-form-item>
        <el-form-item label="新密码">
          <el-input
            v-model="resetPasswordForm.password"
            type="password"
            placeholder="请输入新密码"
            show-password
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="showResetPasswordDialog = false">取消</el-button>
        <el-button
          type="primary"
          :loading="resetPasswordLoading"
          @click="handleResetPasswordConfirm"
        >
          确认重置
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  User,
  UserFilled,
  Plus,
  Download,
  Upload,
  Check,
  Connection,
  Search,
  Delete,
  View,
  Edit,
  More,
  Key,
  Lock,
  Unlock,
  SwitchButton,
  Clock,
  Calendar,
  WarningFilled
} from '@element-plus/icons-vue'
import { useUserStore } from '@/stores/user'
import { useAuthStore } from '@/stores/auth'
import { useRbacStore } from '@/stores/rbac'
import UserDetailDialog from '@/components/admin/UserDetailDialog.vue'
import UserEditDialog from '@/components/admin/UserEditDialog.vue'
import type { User as UserType, Role, UserListParams } from '@/types/user'

// 简单的防抖函数实现
const debounce = (func: Function, wait: number) => {
  let timeout: NodeJS.Timeout
  return function executedFunction(...args: any[]) {
    const later = () => {
      clearTimeout(timeout)
      func(...args)
    }
    clearTimeout(timeout)
    timeout = setTimeout(later, wait)
  }
}

// Store
const userStore = useUserStore()
const authStore = useAuthStore()
const rbacStore = useRbacStore()

// 响应式数据
const loading = ref(false)
const users = computed(() => userStore.users)
const userStats = computed(() => userStore.userStats)
const pagination = computed(() => userStore.pagination)
const roles = ref<Role[]>([])

// 筛选表单
const filterForm = reactive<UserListParams>({
  search: '',
  status: '',
  roleId: undefined,
  orderBy: 'createdAt',
  orderDirection: 'DESC',
  page: 1,
  limit: 20
})

// 选中的用户
const selectedUsers = ref<UserType[]>([])

// 对话框状态
const showDetailDialog = ref(false)
const showEditDialog = ref(false)
const showDeleteDialog = ref(false)
const showBatchDeleteDialog = ref(false)
const showResetPasswordDialog = ref(false)

// 当前操作的用户
const currentUser = ref<UserType | null>(null)
const editingUser = ref<UserType | null>(null)
const deletingUser = ref<UserType | null>(null)
const resetPasswordUser = ref<UserType | null>(null)

// 编辑模式
const editMode = ref<'create' | 'edit'>('create')

// 加载状态
const deleteLoading = ref(false)
const batchDeleteLoading = ref(false)
const resetPasswordLoading = ref(false)

// 重置密码表单
const resetPasswordForm = reactive({
  password: ''
})

// 权限检查
const hasPermission = (permission: string): boolean => {
  return authStore.hasPermission(permission)
}

// 检查是否有任何用户操作权限
const hasAnyUserAction = (): boolean => {
  return hasPermission('user.update') || hasPermission('user.delete')
}

// 获取角色标签类型
const getRoleTagType = (roleName: string): string => {
  const typeMap: Record<string, string> = {
    'super_admin': 'danger',
    'admin': 'warning',
    'editor': 'success',
    'user': 'info'
  }
  return typeMap[roleName] || 'info'
}

// 获取状态标签类型
const getStatusTagType = (status: string): string => {
  const typeMap: Record<string, string> = {
    'active': 'success',
    'inactive': 'warning',
    'banned': 'danger'
  }
  return typeMap[status] || 'info'
}

// 获取状态文本
const getStatusText = (status: string): string => {
  const textMap: Record<string, string> = {
    'active': '活跃',
    'inactive': '禁用',
    'banned': '封禁'
  }
  return textMap[status] || status
}

// 格式化日期
const formatDate = (dateString: string): string => {
  return new Date(dateString).toLocaleString('zh-CN')
}

// 防抖搜索
const handleSearch = debounce(() => {
  filterForm.page = 1
  fetchUsers()
}, 300)

// 筛选处理
const handleFilter = () => {
  filterForm.page = 1
  fetchUsers()
}

// 重置筛选
const handleResetFilter = () => {
  Object.assign(filterForm, {
    search: '',
    status: '',
    roleId: undefined,
    orderBy: 'createdAt',
    orderDirection: 'DESC',
    page: 1,
    limit: 20
  })
  fetchUsers()
}

// 分页处理
const handlePageChange = (page: number) => {
  filterForm.page = page
  fetchUsers()
}

const handlePageSizeChange = (size: number) => {
  filterForm.limit = size
  filterForm.page = 1
  fetchUsers()
}

// 选择变化处理
const handleSelectionChange = (selection: UserType[]) => {
  selectedUsers.value = selection
}

// 用户操作方法
const handleCreateUser = () => {
  editingUser.value = null
  editMode.value = 'create'
  showEditDialog.value = true
}

const handleViewUser = (user: UserType) => {
  currentUser.value = user
  showDetailDialog.value = true
}

const handleEditUser = (user: UserType) => {
  editingUser.value = user
  editMode.value = 'edit'
  showEditDialog.value = true
}

const handleEditSuccess = () => {
  fetchUsers()
  fetchUserStats()
}

const handleDeleteUser = (user: UserType) => {
  deletingUser.value = user
  showDeleteDialog.value = true
}

const handleDeleteCancel = () => {
  showDeleteDialog.value = false
  deletingUser.value = null
}

const handleDeleteConfirm = async () => {
  if (!deletingUser.value) return

  try {
    deleteLoading.value = true
    await userStore.deleteUser(deletingUser.value.id)
    ElMessage.success('用户删除成功')
    showDeleteDialog.value = false
    deletingUser.value = null
    fetchUsers()
    fetchUserStats()
  } catch (error) {
    console.error('Failed to delete user:', error)
    ElMessage.error('删除用户失败')
  } finally {
    deleteLoading.value = false
  }
}

// 批量删除
const handleBatchDelete = () => {
  if (selectedUsers.value.length === 0) {
    ElMessage.warning('请先选择要删除的用户')
    return
  }
  showBatchDeleteDialog.value = true
}

const handleBatchDeleteConfirm = async () => {
  try {
    batchDeleteLoading.value = true
    const userIds = selectedUsers.value.map(user => user.id)
    await userStore.batchDeleteUsers(userIds)
    ElMessage.success(`成功删除 ${selectedUsers.value.length} 个用户`)
    showBatchDeleteDialog.value = false
    selectedUsers.value = []
    fetchUsers()
    fetchUserStats()
  } catch (error) {
    console.error('Failed to batch delete users:', error)
    ElMessage.error('批量删除失败')
  } finally {
    batchDeleteLoading.value = false
  }
}

// 批量分配角色
const handleBatchAssignRole = () => {
  if (selectedUsers.value.length === 0) {
    ElMessage.warning('请先选择要分配角色的用户')
    return
  }
  // TODO: 实现批量分配角色对话框
  ElMessage.info('批量分配角色功能开发中')
}

// 用户操作处理
const handleUserAction = async (command: string, user: UserType) => {
  switch (command) {
    case 'resetPassword':
      handleResetPassword(user)
      break
    case 'disable':
      await handleUpdateUserStatus(user, 'inactive')
      break
    case 'enable':
      await handleUpdateUserStatus(user, 'active')
      break
    case 'forceLogout':
      await handleForceLogout(user)
      break
    case 'delete':
      handleDeleteUser(user)
      break
  }
}

// 重置密码
const handleResetPassword = (user: UserType) => {
  resetPasswordUser.value = user
  resetPasswordForm.password = ''
  showResetPasswordDialog.value = true
}

const handleResetPasswordConfirm = async () => {
  if (!resetPasswordUser.value || !resetPasswordForm.password) {
    ElMessage.warning('请输入新密码')
    return
  }

  try {
    resetPasswordLoading.value = true
    await userStore.resetPassword(resetPasswordUser.value.id, resetPasswordForm.password)
    ElMessage.success('密码重置成功')
    showResetPasswordDialog.value = false
    resetPasswordUser.value = null
    resetPasswordForm.password = ''
  } catch (error) {
    console.error('Failed to reset password:', error)
    ElMessage.error('密码重置失败')
  } finally {
    resetPasswordLoading.value = false
  }
}

// 更新用户状态
const handleUpdateUserStatus = async (user: UserType, status: 'active' | 'inactive' | 'banned') => {
  try {
    await userStore.updateUserStatus(user.id, status)
    ElMessage.success(`用户状态已更新为${getStatusText(status)}`)
    fetchUsers()
  } catch (error) {
    console.error('Failed to update user status:', error)
    ElMessage.error('更新用户状态失败')
  }
}

// 强制下线
const handleForceLogout = async (user: UserType) => {
  try {
    await ElMessageBox.confirm(
      `确定要强制用户 ${user.username} 下线吗？`,
      '确认操作',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    await userStore.forceLogout(user.id)
    ElMessage.success('用户已强制下线')
  } catch (error) {
    if (error !== 'cancel') {
      console.error('Failed to force logout:', error)
      ElMessage.error('强制下线失败')
    }
  }
}

// 导入导出功能
const handleExportUsers = async () => {
  try {
    const blob = await userStore.exportUsers(filterForm)
    const url = window.URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = `users_${new Date().toISOString().split('T')[0]}.xlsx`
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    window.URL.revokeObjectURL(url)
    ElMessage.success('用户数据导出成功')
  } catch (error) {
    console.error('Failed to export users:', error)
    ElMessage.error('导出用户数据失败')
  }
}

const handleImportUsers = () => {
  const input = document.createElement('input')
  input.type = 'file'
  input.accept = '.xlsx,.xls,.csv'
  input.onchange = async (event) => {
    const file = (event.target as HTMLInputElement).files?.[0]
    if (!file) return

    try {
      const result = await userStore.importUsers(file)
      ElMessage.success(`导入成功：${result.success} 个用户，失败：${result.failed} 个`)
      if (result.errors.length > 0) {
        console.warn('Import errors:', result.errors)
      }
      fetchUsers()
      fetchUserStats()
    } catch (error) {
      console.error('Failed to import users:', error)
      ElMessage.error('导入用户数据失败')
    }
  }
  input.click()
}

// 数据获取方法
const fetchUsers = async () => {
  try {
    loading.value = true
    await userStore.fetchUsers(filterForm)
  } catch (error) {
    console.error('Failed to fetch users:', error)
    ElMessage.error('获取用户列表失败')
  } finally {
    loading.value = false
  }
}

const fetchUserStats = async () => {
  try {
    await userStore.fetchUserStats()
  } catch (error) {
    console.error('Failed to fetch user stats:', error)
  }
}

const fetchRoles = async () => {
  try {
    const roleList = await userStore.fetchRoles()
    roles.value = roleList
  } catch (error) {
    console.error('Failed to fetch roles:', error)
  }
}

// 初始化
const init = async () => {
  await Promise.all([
    fetchUsers(),
    fetchUserStats(),
    fetchRoles()
  ])
}

// 组件挂载时初始化
onMounted(() => {
  init()
})
</script>

<style scoped>
.user-management {
  padding: 24px;
  background-color: var(--el-bg-color-page);
  min-height: 100vh;
}

/* 页面头部 */
.page-header {
  margin-bottom: 24px;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: 24px;
}

.header-left {
  flex: 1;
}

.page-title {
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 24px;
  font-weight: 600;
  color: var(--el-text-color-primary);
  margin: 0 0 8px 0;
}

.page-description {
  color: var(--el-text-color-regular);
  margin: 0;
  font-size: 14px;
}

.header-actions {
  display: flex;
  gap: 12px;
  flex-shrink: 0;
}

/* 统计卡片 */
.stats-section {
  margin-bottom: 24px;
}

.stats-card {
  border: 1px solid var(--el-border-color-light);
  transition: all 0.3s ease;
}

.stats-card:hover {
  box-shadow: var(--el-box-shadow-light);
  transform: translateY(-2px);
}

.stats-content {
  display: flex;
  align-items: center;
  gap: 16px;
}

.stats-icon {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  color: white;
}

.stats-icon.total {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.stats-icon.active {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.stats-icon.new {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

.stats-icon.online {
  background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
}

.stats-info {
  flex: 1;
}

.stats-value {
  font-size: 28px;
  font-weight: 700;
  color: var(--el-text-color-primary);
  line-height: 1;
  margin-bottom: 4px;
}

.stats-label {
  font-size: 14px;
  color: var(--el-text-color-regular);
  font-weight: 500;
}

/* 筛选卡片 */
.filter-card {
  margin-bottom: 24px;
  border: 1px solid var(--el-border-color-light);
}

.filter-content {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.filter-form {
  margin: 0;
}

.filter-form .el-form-item {
  margin-bottom: 0;
}

.batch-actions {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 16px;
  background-color: var(--el-color-primary-light-9);
  border: 1px solid var(--el-color-primary-light-7);
  border-radius: 6px;
}

.batch-info {
  font-size: 14px;
  color: var(--el-color-primary);
  font-weight: 500;
}

/* 表格卡片 */
.table-card {
  border: 1px solid var(--el-border-color-light);
}

.table-container {
  min-height: 400px;
}

.user-table {
  width: 100%;
}

/* 用户信息列 */
.user-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.user-avatar {
  flex-shrink: 0;
}

.user-details {
  flex: 1;
  min-width: 0;
}

.user-name {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 4px;
}

.username-link {
  font-weight: 500;
  color: var(--el-color-primary);
  padding: 0;
  height: auto;
  line-height: 1;
}

.username-link:hover {
  color: var(--el-color-primary-dark-2);
}

.nickname-tag {
  font-size: 12px;
}

.user-email {
  font-size: 13px;
  color: var(--el-text-color-regular);
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* 角色列 */
.roles-container {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
}

.role-tag {
  font-size: 12px;
}

.no-roles {
  font-size: 13px;
  color: var(--el-text-color-placeholder);
  font-style: italic;
}

/* 时间列 */
.login-time,
.create-time {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 13px;
  color: var(--el-text-color-regular);
}

.never-login {
  font-size: 13px;
  color: var(--el-text-color-placeholder);
  font-style: italic;
}

/* 操作列 */
.action-buttons {
  display: flex;
  gap: 8px;
  align-items: center;
}

.action-buttons .el-button {
  padding: 4px 8px;
}

/* 空状态 */
.empty-state {
  padding: 60px 20px;
  text-align: center;
}

/* 分页 */
.pagination-container {
  display: flex;
  justify-content: center;
  padding: 24px 0 0 0;
  border-top: 1px solid var(--el-border-color-lighter);
  margin-top: 24px;
}

/* 对话框样式 */
.delete-content,
.batch-delete-content {
  display: flex;
  align-items: flex-start;
  gap: 16px;
  padding: 16px 0;
}

.delete-icon {
  font-size: 24px;
  color: var(--el-color-warning);
  flex-shrink: 0;
  margin-top: 2px;
}

.delete-text {
  flex: 1;
}

.delete-text p {
  margin: 0 0 8px 0;
  line-height: 1.5;
}

.delete-warning {
  font-size: 13px;
  color: var(--el-text-color-regular);
}

.selected-users {
  margin-top: 12px;
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
}

.user-tag {
  font-size: 12px;
}

.more-users {
  font-size: 13px;
  color: var(--el-text-color-regular);
  align-self: center;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .header-content {
    flex-direction: column;
    align-items: stretch;
  }

  .header-actions {
    justify-content: flex-start;
  }
}

@media (max-width: 768px) {
  .user-management {
    padding: 16px;
  }

  .page-title {
    font-size: 20px;
  }

  .stats-section .el-col {
    margin-bottom: 16px;
  }

  .filter-form {
    flex-direction: column;
  }

  .filter-form .el-form-item {
    margin-bottom: 16px;
  }

  .batch-actions {
    flex-direction: column;
    align-items: stretch;
    gap: 8px;
  }

  .action-buttons {
    flex-direction: column;
    gap: 4px;
  }

  .action-buttons .el-button {
    width: 100%;
    justify-content: flex-start;
  }

  /* 移动端隐藏部分列 */
  .user-table .el-table__cell:nth-child(4),
  .user-table .el-table__cell:nth-child(5) {
    display: none;
  }
}

@media (max-width: 480px) {
  .header-actions {
    flex-direction: column;
    gap: 8px;
  }

  .header-actions .el-button {
    width: 100%;
  }

  .stats-content {
    flex-direction: column;
    text-align: center;
    gap: 12px;
  }

  .user-info {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }

  .user-details {
    width: 100%;
  }

  .user-name {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }
}

/* 深色主题适配 */
@media (prefers-color-scheme: dark) {
  .stats-card:hover {
    box-shadow: 0 2px 12px 0 rgba(255, 255, 255, 0.1);
  }

  .batch-actions {
    background-color: rgba(64, 158, 255, 0.1);
    border-color: rgba(64, 158, 255, 0.3);
  }
}

/* 打印样式 */
@media print {
  .user-management {
    padding: 0;
    background: white;
  }

  .page-header,
  .filter-card,
  .pagination-container {
    display: none;
  }

  .stats-section {
    margin-bottom: 16px;
  }

  .action-buttons {
    display: none;
  }
}
</style>