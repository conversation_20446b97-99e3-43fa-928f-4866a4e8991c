import { Request, Response, NextFunction } from 'express';
import { AuthenticatedRequest } from '../middleware/permission';
export declare const getUserRoles: (req: Request, res: Response, next: NextFunction) => Promise<void>;
export declare const getUserPermissions: (req: Request, res: Response, next: NextFunction) => Promise<void>;
export declare const assignUserRoles: (req: AuthenticatedRequest, res: Response, next: NextFunction) => Promise<void>;
export declare const addUserRole: (req: AuthenticatedRequest, res: Response, next: NextFunction) => Promise<void>;
export declare const removeUserRole: (req: Request, res: Response, next: NextFunction) => Promise<void>;
export declare const checkUserPermission: (req: Request, res: Response, next: NextFunction) => Promise<void>;
export declare const checkUserPermissions: (req: Request, res: Response, next: NextFunction) => Promise<void>;
export declare const checkUserRole: (req: Request, res: Response, next: NextFunction) => Promise<void>;
export declare const getUsersByRole: (req: Request, res: Response, next: NextFunction) => Promise<void>;
export declare const getCurrentUserRoles: (req: AuthenticatedRequest, res: Response, next: NextFunction) => Promise<void>;
//# sourceMappingURL=userRole.d.ts.map