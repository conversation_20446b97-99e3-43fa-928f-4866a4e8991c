/**
 * 用户状态枚举
 */
export type UserStatus = 'active' | 'inactive' | 'banned'

/**
 * 用户信息接口（完整版，用于用户管理）
 */
export interface User {
  id: number
  username: string
  email: string
  nickname?: string
  avatar?: string
  isActive: boolean
  status?: UserStatus // 计算属性，基于 isActive
  lastLoginAt?: string
  createdAt: string
  updatedAt: string
  roles?: Role[]
  permissions?: Permission[]
}

/**
 * 基础用户信息接口（简化版，用于认证）
 */
export interface BasicUser {
  id: number
  username: string
  email: string
}

/**
 * 角色信息接口
 */
export interface Role {
  id: number
  name: string
  description?: string
  isActive: boolean
  isSystem: boolean
  createdAt: string
  updatedAt: string
  permissions?: Permission[]
  userCount?: number
}

/**
 * 权限信息接口
 */
export interface Permission {
  id: number
  name: string
  description?: string
  resource: string
  action: string
  isActive: boolean
  createdAt: string
  updatedAt: string
}

/**
 * 用户角色关联接口
 */
export interface UserRole {
  id: number
  userId: number
  roleId: number
  assignedBy: number
  assignedAt: string
  createdAt: string
  updatedAt: string
  user?: BasicUser
  role?: Role
}

/**
 * 创建用户请求接口
 */
export interface CreateUserRequest {
  username: string
  email: string
  password: string
  nickname?: string
  roleIds?: number[]
}

/**
 * 更新用户请求接口
 */
export interface UpdateUserRequest {
  username?: string
  email?: string
  nickname?: string
  status?: UserStatus
  roleIds?: number[]
}

/**
 * 用户列表查询参数接口
 */
export interface UserListParams {
  page?: number
  limit?: number
  search?: string
  status?: string
  roleId?: number
  orderBy?: string
  orderDirection?: 'ASC' | 'DESC'
}

/**
 * 用户列表响应接口
 */
export interface UserListResponse {
  users: User[]
  total: number
  totalPages: number
  currentPage: number
}

/**
 * 用户统计信息接口
 */
export interface UserStats {
  totalUsers: number
  activeUsers: number
  newUsersToday: number
  onlineUsers: number
  usersByRole: Record<string, number>
  usersByStatus: Record<string, number>
  registrationTrend: Array<{
    date: string
    count: number
  }>
}

/**
 * 用户权限信息接口
 */
export interface UserPermissions {
  permissions: Permission[]
  roles: Role[]
}

/**
 * 创建角色请求接口
 */
export interface CreateRoleData {
  name: string
  description?: string
  isActive?: boolean
}

/**
 * 更新角色请求接口
 */
export interface UpdateRoleData {
  name?: string
  description?: string
  isActive?: boolean
}

/**
 * 角色权限分配接口
 */
export interface AssignRoleData {
  userId: number
  roleId: number
}

/**
 * 分页响应接口
 */
export interface PaginatedResponse<T> {
  items: T[]
  pagination: {
    page: number
    limit: number
    total: number
    totalPages: number
    hasNextPage: boolean
    hasPrevPage: boolean
  }
}

/**
 * API响应接口
 */
export interface ApiResponse<T> {
  success: boolean
  data: T
  message?: string
}
