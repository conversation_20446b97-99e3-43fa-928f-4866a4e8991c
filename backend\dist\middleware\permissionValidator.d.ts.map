{"version": 3, "file": "permissionValidator.d.ts", "sourceRoot": "", "sources": ["../../src/middleware/permissionValidator.ts"], "names": [], "mappings": "AAKA,OAAO,EAAW,QAAQ,EAAE,YAAY,EAAE,MAAM,SAAS,CAAA;AACzD,OAAO,EAAE,oBAAoB,EAAE,MAAM,QAAQ,CAAA;AAQ7C,UAAU,0BAA0B;IAClC,WAAW,CAAC,EAAE,MAAM,EAAE,CAAA;IACtB,KAAK,CAAC,EAAE,MAAM,EAAE,CAAA;IAChB,UAAU,CAAC,EAAE,OAAO,CAAA;IACpB,SAAS,CAAC,EAAE,OAAO,CAAA;IACnB,kBAAkB,CAAC,EAAE,MAAM,CAAA;IAC3B,WAAW,CAAC,EAAE,OAAO,CAAA;CACtB;AAOD,eAAO,MAAM,mBAAmB,GAAI,QAAQ,0BAA0B,MACtD,KAAK,oBAAoB,EAAE,KAAK,QAAQ,EAAE,MAAM,YAAY,KAAG,OAAO,CAAC,IAAI,CAwD1F,CAAA;AAwFD,eAAO,MAAM,oBAAoB;8BAhJZ,oBAAoB,OAAO,QAAQ,QAAQ,YAAY,KAAG,OAAO,CAAC,IAAI,CAAC;+BAAvE,oBAAoB,OAAO,QAAQ,QAAQ,YAAY,KAAG,OAAO,CAAC,IAAI,CAAC;oCAkK1D,MAAM,WAlKnB,oBAAoB,OAAO,QAAQ,QAAQ,YAAY,KAAG,OAAO,CAAC,IAAI,CAAC;wCAyKtD,MAAM,EAAE,WAzKzB,oBAAoB,OAAO,QAAQ,QAAQ,YAAY,KAAG,OAAO,CAAC,IAAI,CAAC;yCAiLrD,MAAM,EAAE,WAjL1B,oBAAoB,OAAO,QAAQ,QAAQ,YAAY,KAAG,OAAO,CAAC,IAAI,CAAC;+CAyL/C,MAAM,WAzL9B,oBAAoB,OAAO,QAAQ,QAAQ,YAAY,KAAG,OAAO,CAAC,IAAI,CAAC;oCAkM1D,MAAM,EAAE,WAlMrB,oBAAoB,OAAO,QAAQ,QAAQ,YAAY,KAAG,OAAO,CAAC,IAAI,CAAC;CAuM3F,CAAA;AAMD,cAAM,eAAe;IACnB,OAAO,CAAC,KAAK,CAA4D;IACzE,OAAO,CAAC,QAAQ,CAAC,GAAG,CAAgB;IAKpC,GAAG,CAAC,GAAG,EAAE,MAAM,GAAG,OAAO,GAAG,IAAI;IAehC,GAAG,CAAC,GAAG,EAAE,MAAM,EAAE,MAAM,EAAE,OAAO,GAAG,IAAI;IAUvC,KAAK,IAAI,IAAI;IAOb,cAAc,CAAC,MAAM,EAAE,MAAM,GAAG,IAAI;CAOrC;AAED,eAAO,MAAM,eAAe,iBAAwB,CAAA;AAOpD,eAAO,MAAM,4BAA4B,GAAI,QAAQ,0BAA0B,MAC/D,KAAK,oBAAoB,EAAE,KAAK,QAAQ,EAAE,MAAM,YAAY,KAAG,OAAO,CAAC,IAAI,CAuC1F,CAAA"}