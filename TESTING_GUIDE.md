# 用户管理页面测试指南

## 🔧 修复完成的问题

### ✅ 已解决的错误

1. **权限检查方法缺失**
   - 错误: `authStore.hasPermission is not a function`
   - 解决: 在 `authStore` 中添加了 `hasPermission` 方法

2. **API 路径重复**
   - 错误: `GET http://localhost:8080/api/api/admin/users 404`
   - 解决: 修改了 `userService.ts` 中的所有 API 路径

3. **后端 API 端点缺失**
   - 解决: 添加了批量删除、重置密码、状态更新等 API 端点

4. **类型不匹配**
   - 解决: 统一了前后端的数据类型定义

## 🧪 测试步骤

### 1. 启动应用

```bash
# 启动后端
cd backend
npm run dev

# 启动前端
cd frontend
npm run dev
```

### 2. 访问用户管理页面

1. 打开浏览器访问 `http://localhost:5173`
2. 登录系统（如果需要）
3. 导航到用户管理页面 `/admin/users`

### 3. 功能测试清单

#### 基础功能测试
- [ ] 页面正常加载，无控制台错误
- [ ] 统计卡片显示数据
- [ ] 用户列表正常显示
- [ ] 分页功能正常工作

#### 搜索和筛选测试
- [ ] 搜索框输入关键词能正常搜索
- [ ] 状态筛选器正常工作
- [ ] 角色筛选器正常工作
- [ ] 排序功能正常工作
- [ ] 重置筛选功能正常

#### 用户操作测试
- [ ] 点击用户名能打开详情对话框
- [ ] 编辑按钮能打开编辑对话框
- [ ] 删除按钮能显示确认对话框
- [ ] 更多操作下拉菜单正常显示

#### 批量操作测试
- [ ] 选择多个用户后显示批量操作栏
- [ ] 批量删除功能正常工作
- [ ] 批量分配角色功能正常工作

#### 权限控制测试
- [ ] 根据用户权限显示/隐藏操作按钮
- [ ] 无权限用户无法执行受限操作

## 🔍 API 测试

### 测试 API 端点

使用浏览器开发者工具的网络面板，验证以下 API 调用：

1. **获取用户列表**
   ```
   GET /api/users?search=&status=&orderBy=createdAt&orderDirection=DESC&page=1&limit=20
   ```

2. **获取用户统计**
   ```
   GET /api/users/stats
   ```

3. **获取角色列表**
   ```
   GET /api/roles
   ```

4. **创建用户**
   ```
   POST /api/users
   ```

5. **更新用户**
   ```
   PUT /api/users/:id
   ```

6. **删除用户**
   ```
   DELETE /api/users/:id
   ```

7. **批量删除用户**
   ```
   POST /api/users/bulk/delete
   ```

8. **重置密码**
   ```
   POST /api/users/:id/reset-password
   ```

9. **更新用户状态**
   ```
   PATCH /api/users/:id/status
   ```

## 🐛 常见问题排查

### 1. 页面无法加载

**症状**: 页面显示空白或加载错误

**排查步骤**:
1. 检查控制台是否有 JavaScript 错误
2. 检查网络面板是否有 API 请求失败
3. 确认后端服务是否正常运行
4. 检查用户是否已登录

### 2. API 请求失败

**症状**: 网络面板显示 404 或其他错误

**排查步骤**:
1. 检查 API 路径是否正确
2. 确认后端路由是否已正确配置
3. 检查认证 token 是否有效
4. 验证请求参数是否正确

### 3. 权限相关问题

**症状**: 某些功能无法使用或按钮不显示

**排查步骤**:
1. 检查用户是否有相应权限
2. 确认权限检查逻辑是否正确
3. 验证后端权限中间件是否正常工作

### 4. 数据显示问题

**症状**: 数据格式错误或显示异常

**排查步骤**:
1. 检查前后端数据类型是否匹配
2. 确认数据转换逻辑是否正确
3. 验证数据库中的数据是否正确

## 📊 性能测试

### 1. 大数据量测试

1. 创建大量测试用户（建议 1000+ 条记录）
2. 测试分页加载性能
3. 测试搜索功能响应时间
4. 验证内存使用情况

### 2. 并发测试

1. 多个用户同时访问用户管理页面
2. 测试同时进行 CRUD 操作
3. 验证数据一致性

## 🔒 安全测试

### 1. 权限测试

1. 使用不同权限级别的用户测试
2. 尝试访问无权限的功能
3. 验证 API 权限控制

### 2. 输入验证测试

1. 测试各种异常输入
2. 验证 XSS 防护
3. 测试 SQL 注入防护

## 📝 测试报告模板

### 测试环境
- 浏览器: 
- 操作系统: 
- 前端版本: 
- 后端版本: 

### 测试结果
- [ ] 基础功能正常
- [ ] API 调用正常
- [ ] 权限控制正常
- [ ] 性能表现良好
- [ ] 安全测试通过

### 发现的问题
1. 问题描述
   - 重现步骤
   - 预期结果
   - 实际结果
   - 严重程度

### 建议改进
1. 功能改进建议
2. 性能优化建议
3. 用户体验改进建议

## 🚀 部署前检查

在部署到生产环境前，请确保：

- [ ] 所有测试用例通过
- [ ] 性能测试满足要求
- [ ] 安全测试通过
- [ ] 代码审查完成
- [ ] 文档更新完整
- [ ] 备份策略就绪

## 📞 支持联系

如果在测试过程中遇到问题，请：

1. 检查本文档的常见问题部分
2. 查看控制台错误信息
3. 记录详细的重现步骤
4. 提供测试环境信息

通过系统的测试，确保用户管理页面的稳定性和可靠性！
