import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { authService } from '@/services/auth'
import type { BasicUser } from '@/types/user'
import { useUIStore } from '@/stores/ui'

/**
 * 认证状态管理存储
 * 管理用户认证状态、登录、登出和权限检查等功能
 */
export const useAuthStore = defineStore('auth', () => {
  // 用户信息响应式引用
  const user = ref<BasicUser | null>(null)
  // 认证令牌响应式引用，从本地存储初始化
  const token = ref<string | null>(localStorage.getItem('token'))

  // 计算属性：判断用户是否已认证
  const isAuthenticated = computed(() => !!token.value)

  /**
   * 用户登录函数
   * @param username - 用户名
   * @param password - 密码
   * @returns 登录结果对象，包含成功状态和错误信息
   */
  const login = async (username: string, password: string) => {
    const uiStore = useUIStore()
    try {
      // 调用认证服务进行登录
      const response = await authService.login(username, password)
      token.value = response.token
      user.value = response.user
      // 将令牌保存到本地存储
      localStorage.setItem('token', response.token)
      uiStore.showSuccess('登录成功')
      return { success: true }
    } catch (error: any) {
      // 返回登录失败信息
      return { success: false, error: error.message }
    }
  }

  /**
   * 用户登出函数
   * 清除认证信息和本地存储的令牌
   */
  const logout = () => {
    const uiStore = useUIStore()
    token.value = null
    user.value = null
    // 从本地存储移除令牌
    localStorage.removeItem('token')
    uiStore.showInfo('已退出登录')
  }

  /**
   * 检查认证状态函数
   * 验证当前令牌有效性并获取用户信息
   * @returns 认证状态布尔值
   */
  const checkAuth = async () => {
    // 如果没有令牌直接返回false
    if (!token.value) return false

    try {
      // 获取用户档案信息验证令牌
      const response = await authService.getProfile()
      user.value = response.user
      return true
    } catch (error) {
      // 认证失败时执行登出操作
      logout()
      return false
    }
  }

  /**
   * 检查用户是否具有指定权限
   * @param permission - 权限名称
   * @returns 是否具有权限
   */
  const hasPermission = (permission: string): boolean => {
    if (!user.value) return false

    try {
      // 临时实现：所有已登录用户都有权限，用于测试
      // 实际项目中应该实现真正的权限检查逻辑
      // 可以调用 rbacStore 的权限检查方法或者从用户信息中获取权限
      console.log(`Checking permission: ${permission} for user: ${user.value.username}`)
      return true
    } catch (error) {
      console.error('Permission check failed:', error)
      return false
    }
  }

  return {
    user,
    token,
    isAuthenticated,
    login,
    logout,
    checkAuth,
    hasPermission
  }
})