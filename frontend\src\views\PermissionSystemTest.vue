<template>
  <div class="permission-test">
    <el-card class="mb-4">
      <template #header>
        <h2>权限系统完整测试</h2>
      </template>
      
      <!-- 系统状态 -->
      <el-row :gutter="20" class="mb-4">
        <el-col :span="8">
          <el-card>
            <template #header>
              <h3>系统状态</h3>
            </template>
            <el-descriptions :column="1" border>
              <el-descriptions-item label="后端服务">
                <el-tag :type="backendStatus ? 'success' : 'danger'">
                  {{ backendStatus ? '正常' : '异常' }}
                </el-tag>
              </el-descriptions-item>
              <el-descriptions-item label="权限缓存">
                <el-tag :type="cacheStatus ? 'success' : 'warning'">
                  {{ cacheStatus ? '已启用' : '未启用' }}
                </el-tag>
              </el-descriptions-item>
              <el-descriptions-item label="批量检查">
                <el-tag :type="batchCheckSupported ? 'success' : 'warning'">
                  {{ batchCheckSupported ? '支持' : '不支持' }}
                </el-tag>
              </el-descriptions-item>
            </el-descriptions>
          </el-card>
        </el-col>
        
        <el-col :span="8">
          <el-card>
            <template #header>
              <h3>性能指标</h3>
            </template>
            <el-descriptions :column="1" border>
              <el-descriptions-item label="单次检查耗时">
                {{ singleCheckTime }}ms
              </el-descriptions-item>
              <el-descriptions-item label="批量检查耗时">
                {{ batchCheckTime }}ms
              </el-descriptions-item>
              <el-descriptions-item label="缓存命中率">
                {{ cacheHitRate }}%
              </el-descriptions-item>
            </el-descriptions>
          </el-card>
        </el-col>
        
        <el-col :span="8">
          <el-card>
            <template #header>
              <h3>用户信息</h3>
            </template>
            <el-descriptions :column="1" border>
              <el-descriptions-item label="用户ID">
                {{ authStore.user?.id || 'N/A' }}
              </el-descriptions-item>
              <el-descriptions-item label="用户名">
                {{ authStore.user?.username || 'N/A' }}
              </el-descriptions-item>
              <el-descriptions-item label="角色数量">
                {{ userRoles.length }}
              </el-descriptions-item>
              <el-descriptions-item label="权限数量">
                {{ userPermissions.length }}
              </el-descriptions-item>
            </el-descriptions>
          </el-card>
        </el-col>
      </el-row>

      <!-- 权限测试 -->
      <el-card class="mb-4">
        <template #header>
          <h3>权限检查测试</h3>
        </template>
        
        <el-row :gutter="20">
          <el-col :span="12">
            <h4>单个权限检查</h4>
            <el-space direction="vertical" style="width: 100%">
              <el-input
                v-model="testPermission"
                placeholder="输入权限名称，如: user.create"
                @keyup.enter="testSinglePermission"
              >
                <template #append>
                  <el-button @click="testSinglePermission" :loading="testing.single">
                    检查
                  </el-button>
                </template>
              </el-input>
              <div v-if="singleTestResult !== null" class="test-result">
                <el-tag :type="singleTestResult ? 'success' : 'danger'">
                  {{ singleTestResult ? '有权限' : '无权限' }}
                </el-tag>
                <span class="ml-2">耗时: {{ singleCheckTime }}ms</span>
              </div>
            </el-space>
          </el-col>
          
          <el-col :span="12">
            <h4>批量权限检查</h4>
            <el-space direction="vertical" style="width: 100%">
              <el-input
                v-model="testPermissions"
                type="textarea"
                :rows="3"
                placeholder="输入多个权限，每行一个"
              />
              <el-button @click="testBatchPermissions" :loading="testing.batch">
                批量检查
              </el-button>
              <div v-if="batchTestResults" class="test-result">
                <div v-for="(result, permission) in batchTestResults" :key="permission" class="mb-1">
                  <el-tag :type="result ? 'success' : 'danger'" size="small">
                    {{ permission }}: {{ result ? '有权限' : '无权限' }}
                  </el-tag>
                </div>
                <div class="mt-2">
                  <span>总耗时: {{ batchCheckTime }}ms</span>
                </div>
              </div>
            </el-space>
          </el-col>
        </el-row>
      </el-card>

      <!-- 角色测试 -->
      <el-card class="mb-4">
        <template #header>
          <h3>角色检查测试</h3>
        </template>
        
        <el-row :gutter="20">
          <el-col :span="24">
            <h4>角色检查</h4>
            <el-space wrap>
              <el-button
                v-for="role in testRoles"
                :key="role"
                @click="testRole(role)"
                :type="roleTestResults[role] === true ? 'success' : roleTestResults[role] === false ? 'danger' : 'default'"
                :loading="testing.roles[role]"
              >
                {{ role }}
              </el-button>
            </el-space>
          </el-col>
        </el-row>
      </el-card>

      <!-- 缓存测试 -->
      <el-card class="mb-4">
        <template #header>
          <h3>缓存性能测试</h3>
        </template>
        
        <el-row :gutter="20">
          <el-col :span="24">
            <el-space>
              <el-button @click="testCachePerformance" :loading="testing.cache">
                测试缓存性能
              </el-button>
              <el-button @click="clearCache" type="warning">
                清除缓存
              </el-button>
            </el-space>
            
            <div v-if="cacheTestResults" class="mt-4">
              <h4>缓存测试结果</h4>
              <el-descriptions :column="2" border>
                <el-descriptions-item label="首次检查耗时">
                  {{ cacheTestResults.firstCheck }}ms
                </el-descriptions-item>
                <el-descriptions-item label="缓存检查耗时">
                  {{ cacheTestResults.cachedCheck }}ms
                </el-descriptions-item>
                <el-descriptions-item label="性能提升">
                  {{ cacheTestResults.improvement }}x
                </el-descriptions-item>
                <el-descriptions-item label="缓存命中">
                  {{ cacheTestResults.cacheHit ? '是' : '否' }}
                </el-descriptions-item>
              </el-descriptions>
            </div>
          </el-col>
        </el-row>
      </el-card>

      <!-- 权限指令测试 -->
      <el-card class="mb-4">
        <template #header>
          <h3>权限指令测试</h3>
        </template>
        
        <el-row :gutter="20">
          <el-col :span="8">
            <h4>v-permission 指令</h4>
            <el-space direction="vertical">
              <el-button v-permission="'user.create'" type="primary">
                创建用户 (user.create)
              </el-button>
              <el-button v-permission="['user.update', 'user.delete']" type="warning">
                用户管理 (update OR delete)
              </el-button>
              <el-button v-permission="{ permissions: 'admin.settings', mode: 'disable' }" type="danger">
                管理设置 (禁用模式)
              </el-button>
            </el-space>
          </el-col>
          
          <el-col :span="8">
            <h4>v-role 指令</h4>
            <el-space direction="vertical">
              <el-button v-role="'super_admin'" type="danger">
                超级管理员功能
              </el-button>
              <el-button v-role="['admin', 'editor']" type="warning">
                管理员/编辑者功能
              </el-button>
              <el-button v-role="'user'" type="info">
                普通用户功能
              </el-button>
            </el-space>
          </el-col>
          
          <el-col :span="8">
            <h4>v-auth 指令</h4>
            <el-space direction="vertical">
              <el-button v-auth type="success">
                需要登录
              </el-button>
              <el-button v-auth="false" type="info">
                未登录显示
              </el-button>
            </el-space>
          </el-col>
        </el-row>
      </el-card>

      <!-- 操作按钮 -->
      <el-card>
        <template #header>
          <h3>系统操作</h3>
        </template>
        
        <el-space>
          <el-button @click="runFullTest" :loading="testing.full" type="primary">
            运行完整测试
          </el-button>
          <el-button @click="generateReport" type="success">
            生成测试报告
          </el-button>
          <el-button @click="resetTests" type="warning">
            重置测试
          </el-button>
        </el-space>
      </el-card>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { useAuthStore } from '@/stores/auth'
import { useRbacStore } from '@/stores/rbac'
import { usePermission } from '@/composables/usePermission'
import { clearPermissionCache } from '@/directives/permission'

const authStore = useAuthStore()
const rbacStore = useRbacStore()
const { userPermissions, userRoles, loadUserPermissions } = usePermission()

// 测试状态
const testing = reactive({
  single: false,
  batch: false,
  cache: false,
  full: false,
  roles: {} as Record<string, boolean>
})

// 系统状态
const backendStatus = ref(true)
const cacheStatus = ref(true)
const batchCheckSupported = ref(true)

// 性能指标
const singleCheckTime = ref(0)
const batchCheckTime = ref(0)
const cacheHitRate = ref(0)

// 测试数据
const testPermission = ref('user.create')
const testPermissions = ref('user.create\nuser.read\nuser.update\nuser.delete')
const testRoles = ['super_admin', 'admin', 'editor', 'user']

// 测试结果
const singleTestResult = ref<boolean | null>(null)
const batchTestResults = ref<Record<string, boolean> | null>(null)
const roleTestResults = reactive<Record<string, boolean | null>>({})
const cacheTestResults = ref<any>(null)

// 测试单个权限
const testSinglePermission = async () => {
  if (!authStore.user || !testPermission.value.trim()) return
  
  testing.single = true
  const startTime = Date.now()
  
  try {
    const result = await rbacStore.checkUserPermission(authStore.user.id, testPermission.value.trim())
    singleTestResult.value = result
    singleCheckTime.value = Date.now() - startTime
  } catch (error) {
    console.error('权限检查失败:', error)
    singleTestResult.value = false
  } finally {
    testing.single = false
  }
}

// 测试批量权限
const testBatchPermissions = async () => {
  if (!authStore.user || !testPermissions.value.trim()) return
  
  testing.batch = true
  const startTime = Date.now()
  
  try {
    const permissions = testPermissions.value.split('\n').map(p => p.trim()).filter(Boolean)
    const results = await rbacStore.checkUserPermissions(authStore.user.id, permissions)
    batchTestResults.value = results
    batchCheckTime.value = Date.now() - startTime
  } catch (error) {
    console.error('批量权限检查失败:', error)
    batchTestResults.value = null
  } finally {
    testing.batch = false
  }
}

// 测试角色
const testRole = async (role: string) => {
  if (!authStore.user) return
  
  testing.roles[role] = true
  
  try {
    const userRolesList = await rbacStore.getUserRolesByUserId(authStore.user.id)
    const hasRole = userRolesList.some(r => r.name === role)
    roleTestResults[role] = hasRole
  } catch (error) {
    console.error('角色检查失败:', error)
    roleTestResults[role] = false
  } finally {
    testing.roles[role] = false
  }
}

// 测试缓存性能
const testCachePerformance = async () => {
  if (!authStore.user) return
  
  testing.cache = true
  
  try {
    const testPerm = 'user.read'
    
    // 清除缓存
    clearCache()
    
    // 首次检查（无缓存）
    const start1 = Date.now()
    await rbacStore.checkUserPermission(authStore.user.id, testPerm)
    const firstCheck = Date.now() - start1
    
    // 第二次检查（有缓存）
    const start2 = Date.now()
    await rbacStore.checkUserPermission(authStore.user.id, testPerm)
    const cachedCheck = Date.now() - start2
    
    cacheTestResults.value = {
      firstCheck,
      cachedCheck,
      improvement: Math.round((firstCheck / Math.max(cachedCheck, 1)) * 100) / 100,
      cacheHit: cachedCheck < firstCheck
    }
  } catch (error) {
    console.error('缓存测试失败:', error)
  } finally {
    testing.cache = false
  }
}

// 清除缓存
const clearCache = () => {
  clearPermissionCache()
  rbacStore.clearPermissionCache()
  console.log('权限缓存已清除')
}

// 运行完整测试
const runFullTest = async () => {
  testing.full = true
  
  try {
    // 测试系统状态
    await checkSystemStatus()
    
    // 测试权限检查
    await testSinglePermission()
    await testBatchPermissions()
    
    // 测试所有角色
    for (const role of testRoles) {
      await testRole(role)
    }
    
    // 测试缓存性能
    await testCachePerformance()
    
    console.log('完整测试完成')
  } catch (error) {
    console.error('完整测试失败:', error)
  } finally {
    testing.full = false
  }
}

// 检查系统状态
const checkSystemStatus = async () => {
  try {
    // 检查后端服务
    if (authStore.user) {
      await rbacStore.getUserRolesByUserId(authStore.user.id)
      backendStatus.value = true
    }
    
    // 检查批量检查支持
    if (authStore.user && rbacStore.checkUserPermissions) {
      batchCheckSupported.value = true
    }
  } catch (error) {
    backendStatus.value = false
    console.error('系统状态检查失败:', error)
  }
}

// 生成测试报告
const generateReport = () => {
  const report = {
    timestamp: new Date().toISOString(),
    user: {
      id: authStore.user?.id,
      username: authStore.user?.username,
      roles: userRoles.value,
      permissions: userPermissions.value.length
    },
    systemStatus: {
      backend: backendStatus.value,
      cache: cacheStatus.value,
      batchSupport: batchCheckSupported.value
    },
    performance: {
      singleCheck: singleCheckTime.value,
      batchCheck: batchCheckTime.value,
      cacheHitRate: cacheHitRate.value
    },
    testResults: {
      singlePermission: singleTestResult.value,
      batchPermissions: batchTestResults.value,
      roles: roleTestResults,
      cache: cacheTestResults.value
    }
  }
  
  console.log('权限系统测试报告:', report)
  
  // 下载报告
  const blob = new Blob([JSON.stringify(report, null, 2)], { type: 'application/json' })
  const url = URL.createObjectURL(blob)
  const a = document.createElement('a')
  a.href = url
  a.download = `permission-test-report-${Date.now()}.json`
  a.click()
  URL.revokeObjectURL(url)
}

// 重置测试
const resetTests = () => {
  singleTestResult.value = null
  batchTestResults.value = null
  Object.keys(roleTestResults).forEach(key => {
    roleTestResults[key] = null
  })
  cacheTestResults.value = null
  singleCheckTime.value = 0
  batchCheckTime.value = 0
  cacheHitRate.value = 0
}

// 初始化
onMounted(async () => {
  await loadUserPermissions()
  await checkSystemStatus()
})
</script>

<style scoped>
.permission-test {
  padding: 20px;
  max-width: 1400px;
  margin: 0 auto;
}

.mb-4 {
  margin-bottom: 16px;
}

.mb-1 {
  margin-bottom: 4px;
}

.mt-2 {
  margin-top: 8px;
}

.mt-4 {
  margin-top: 16px;
}

.ml-2 {
  margin-left: 8px;
}

.test-result {
  padding: 8px;
  background-color: var(--el-bg-color-page);
  border-radius: 4px;
  border: 1px solid var(--el-border-color);
}

h2 {
  margin: 0;
  color: var(--el-text-color-primary);
}

h3 {
  margin: 0 0 16px 0;
  color: var(--el-text-color-regular);
  font-size: 16px;
}

h4 {
  margin: 0 0 12px 0;
  color: var(--el-text-color-regular);
  font-size: 14px;
}
</style>
