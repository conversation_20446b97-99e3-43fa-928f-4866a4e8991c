"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.validatePermissionsWithCache = exports.permissionCache = exports.PermissionValidators = exports.validatePermissions = void 0;
const errorHandler_1 = require("./errorHandler");
const UserRole_1 = require("../models/UserRole");
const User_1 = require("../models/User");
const validatePermissions = (config) => {
    return async (req, res, next) => {
        try {
            if (!req.user || !req.user.id) {
                throw (0, errorHandler_1.createError)(401, '需要登录才能访问此资源', 'AUTHENTICATION_REQUIRED');
            }
            const userId = req.user.id;
            const { permissions, roles, requireAll = false, allowSelf = false, resourceOwnerField, skipIfOwner = false } = config;
            if (allowSelf || skipIfOwner) {
                const isOwner = await checkResourceOwnership(req, userId, resourceOwnerField);
                if (isOwner) {
                    if (skipIfOwner) {
                        return next();
                    }
                    if (allowSelf && !permissions && !roles) {
                        return next();
                    }
                }
            }
            const user = await User_1.User.findByPk(userId);
            if (!user || !user.isActive) {
                throw (0, errorHandler_1.createError)(403, '用户账户已被禁用', 'USER_INACTIVE');
            }
            if (roles && roles.length > 0) {
                const hasRequiredRoles = await checkUserRoles(userId, roles, requireAll);
                if (!hasRequiredRoles) {
                    throw (0, errorHandler_1.createError)(403, `需要以下角色${requireAll ? '全部' : '之一'}: ${roles.join(', ')}`, 'INSUFFICIENT_ROLES');
                }
            }
            if (permissions && permissions.length > 0) {
                const hasRequiredPermissions = await checkUserPermissions(userId, permissions, requireAll);
                if (!hasRequiredPermissions) {
                    throw (0, errorHandler_1.createError)(403, `缺少以下权限${requireAll ? '全部' : '之一'}: ${permissions.join(', ')}`, 'INSUFFICIENT_PERMISSIONS');
                }
            }
            if (process.env.NODE_ENV === 'development') {
                console.log(`权限验证通过 - 用户: ${userId}, 权限: ${permissions?.join(',') || 'N/A'}, 角色: ${roles?.join(',') || 'N/A'}`);
            }
            next();
        }
        catch (error) {
            next(error);
        }
    };
};
exports.validatePermissions = validatePermissions;
async function checkUserRoles(userId, requiredRoles, requireAll) {
    try {
        const userRoles = await UserRole_1.UserRole.getUserRoles(userId);
        const userRoleNames = userRoles.map((role) => role.name);
        if (requireAll) {
            return requiredRoles.every(role => userRoleNames.includes(role));
        }
        else {
            return requiredRoles.some(role => userRoleNames.includes(role));
        }
    }
    catch (error) {
        console.error('检查用户角色失败:', error);
        return false;
    }
}
async function checkUserPermissions(userId, requiredPermissions, requireAll) {
    try {
        if (requireAll) {
            for (const permission of requiredPermissions) {
                const hasPermission = await UserRole_1.UserRole.hasPermission(userId, permission);
                if (!hasPermission) {
                    return false;
                }
            }
            return true;
        }
        else {
            for (const permission of requiredPermissions) {
                const hasPermission = await UserRole_1.UserRole.hasPermission(userId, permission);
                if (hasPermission) {
                    return true;
                }
            }
            return false;
        }
    }
    catch (error) {
        console.error('检查用户权限失败:', error);
        return false;
    }
}
async function checkResourceOwnership(req, userId, ownerField) {
    try {
        if (!ownerField) {
            const resourceUserId = parseInt(req.params.userId || req.params.id || '0');
            return resourceUserId === userId;
        }
        return false;
    }
    catch (error) {
        console.error('检查资源所有权失败:', error);
        return false;
    }
}
exports.PermissionValidators = {
    requireAdmin: () => (0, exports.validatePermissions)({
        roles: ['super_admin', 'admin']
    }),
    requireEditor: () => (0, exports.validatePermissions)({
        roles: ['super_admin', 'admin', 'editor']
    }),
    requirePermission: (permission) => (0, exports.validatePermissions)({
        permissions: [permission]
    }),
    requireAnyPermission: (permissions) => (0, exports.validatePermissions)({
        permissions,
        requireAll: false
    }),
    requireAllPermissions: (permissions) => (0, exports.validatePermissions)({
        permissions,
        requireAll: true
    }),
    requireOwnershipOrPermission: (permission) => (0, exports.validatePermissions)({
        permissions: [permission],
        allowSelf: true,
        skipIfOwner: true
    }),
    requireRoleOrOwnership: (roles) => (0, exports.validatePermissions)({
        roles,
        allowSelf: true,
        skipIfOwner: true
    })
};
class PermissionCache {
    constructor() {
        this.cache = new Map();
        this.TTL = 5 * 60 * 1000;
    }
    get(key) {
        const cached = this.cache.get(key);
        if (!cached)
            return null;
        if (Date.now() - cached.timestamp > this.TTL) {
            this.cache.delete(key);
            return null;
        }
        return cached.result;
    }
    set(key, result) {
        this.cache.set(key, {
            result,
            timestamp: Date.now()
        });
    }
    clear() {
        this.cache.clear();
    }
    clearUserCache(userId) {
        for (const [key] of this.cache) {
            if (key.startsWith(`user_${userId}_`)) {
                this.cache.delete(key);
            }
        }
    }
}
exports.permissionCache = new PermissionCache();
const validatePermissionsWithCache = (config) => {
    return async (req, res, next) => {
        try {
            if (!req.user || !req.user.id) {
                throw (0, errorHandler_1.createError)(401, '需要登录才能访问此资源', 'AUTHENTICATION_REQUIRED');
            }
            const userId = req.user.id;
            const cacheKey = `user_${userId}_${JSON.stringify(config)}`;
            const cachedResult = exports.permissionCache.get(cacheKey);
            if (cachedResult !== null) {
                if (cachedResult) {
                    return next();
                }
                else {
                    throw (0, errorHandler_1.createError)(403, '权限不足', 'INSUFFICIENT_PERMISSIONS');
                }
            }
            const originalNext = next;
            let hasPermission = false;
            const mockNext = (error) => {
                if (!error) {
                    hasPermission = true;
                    exports.permissionCache.set(cacheKey, true);
                    originalNext();
                }
                else {
                    exports.permissionCache.set(cacheKey, false);
                    originalNext(error);
                }
            };
            await (0, exports.validatePermissions)(config)(req, res, mockNext);
        }
        catch (error) {
            next(error);
        }
    };
};
exports.validatePermissionsWithCache = validatePermissionsWithCache;
//# sourceMappingURL=permissionValidator.js.map