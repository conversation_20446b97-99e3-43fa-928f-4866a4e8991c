import { createRouter, createWebHistory } from 'vue-router'
import { useAuthStore } from '@/stores/auth'
import { useUIStore } from '@/stores/ui'
import { useRbacStore } from '@/stores/rbac'
import {
  USER_PERMISSIONS,
  ARTICLE_PERMISSIONS,
  CATEGORY_PERMISSIONS,
  TAG_PERMISSIONS,
  COMMENT_PERMISSIONS,
  POST_PERMISSIONS,
  MEDIA_PERMISSIONS,
  NOTIFICATION_PERMISSIONS,
  ROLE_PERMISSIONS,
  PERMISSION_PERMISSIONS,
  AUDIT_LOG_PERMISSIONS,
  SYSTEM_PERMISSIONS,
  ROLES
} from '@/constants/permissions'

const router = createRouter({
  history: createWebHistory(),
  routes: [
    {
      path: '/',
      name: 'Home',
      component: () => import('@/views/blog/Home.vue')
    },
    {
      path: '/article/:id',
      name: 'ArticleDetail',
      component: () => import('@/views/blog/ArticleDetail.vue'),
      props: true
    },
    {
      path: '/article',
      name: 'ArticleList',
      component: () => import('@/views/blog/ArticleList.vue')
    },
    {
      path: '/tags',
      name: 'TagList',
      component: () => import('@/views/blog/TagList.vue')
    },
    {
      path: '/category',
      name: 'CategoryList',
      component: () => import('@/views/blog/CategoryList.vue')
    },
    {
      path: '/category/:slug',
      name: 'CategoryDetail',
      component: () => import('@/views/blog/CategoryDetail.vue'),
      props: true
    },
    {
      path: '/tag/:name',
      name: 'Tag',
      component: () => import('@/views/blog/TagDetail.vue')
    },
    {
      path: '/posts',
      name: 'Posts',
      component: () => import('@/views/blog/Posts.vue')
    },
    {
      path: '/posts/:id',
      name: 'PostDetail',
      component: () => import('@/views/blog/PostDetail.vue'),
      props: true
    },
    {
      path: '/login',
      name: 'Login',
      component: () => import('@/views/auth/Login.vue')
    },
    {
      path: '/design-system',
      name: 'DesignSystem',
      component: () => import('@/views/DesignSystem.vue')
    },
    {
      path: '/navbar-demo',
      name: 'NavbarDemo',
      component: () => import('@/views/NavbarDemo.vue'),
      meta: {
        title: '导航栏演示',
        hidden: true // 开发测试页面，不在菜单中显示
      }
    },
    {
      path: '/permission-system-test',
      name: 'PermissionSystemTest',
      component: () => import('@/views/PermissionSystemTest.vue'),
      meta: {
        requiresAuth: true,
        title: '权限系统测试',
        hidden: true // 开发测试页面，不在菜单中显示
      }
    },
    {
      path: '/admin',
      name: 'Admin',
      component: () => import('@/views/admin/index.vue'),
      meta: {
        requiresAuth: true,
        title: '管理后台',
        icon: 'Setting',
        roles: [ROLES.SUPER_ADMIN, ROLES.ADMIN, ROLES.EDITOR]
      }
    },
    {
      path: '/admin/articles',
      name: 'AdminArticles',
      component: () => import('@/views/admin/Articles.vue'),
      meta: {
        requiresAuth: true,
        title: '文章管理',
        icon: 'Document',
        permissions: [ARTICLE_PERMISSIONS.LIST, ARTICLE_PERMISSIONS.READ],
        roles: [ROLES.SUPER_ADMIN, ROLES.ADMIN, ROLES.EDITOR]
      }
    },
    {
      path: '/admin/articles/new',
      name: 'AdminArticleNew',
      component: () => import('@/views/admin/ArticleEdit.vue'),
      meta: {
        requiresAuth: true,
        title: '新建文章',
        icon: 'EditPen',
        permissions: [ARTICLE_PERMISSIONS.CREATE],
        roles: [ROLES.SUPER_ADMIN, ROLES.ADMIN, ROLES.EDITOR],
        hidden: true // 不在菜单中显示
      }
    },
    {
      path: '/admin/articles/:id/edit',
      name: 'AdminArticleEdit',
      component: () => import('@/views/admin/ArticleEdit.vue'),
      meta: {
        requiresAuth: true,
        title: '编辑文章',
        icon: 'Edit',
        permissions: [ARTICLE_PERMISSIONS.UPDATE],
        roles: [ROLES.SUPER_ADMIN, ROLES.ADMIN, ROLES.EDITOR],
        hidden: true // 不在菜单中显示
      },
      props: true
    },
    {
      path: '/admin/categories',
      name: 'AdminCategories',
      component: () => import('@/views/admin/Categories.vue'),
      meta: {
        requiresAuth: true,
        title: '分类管理',
        icon: 'FolderOpened',
        permissions: [CATEGORY_PERMISSIONS.LIST, CATEGORY_PERMISSIONS.READ],
        roles: [ROLES.SUPER_ADMIN, ROLES.ADMIN, ROLES.EDITOR]
      }
    },
    {
      path: '/admin/tags',
      name: 'AdminTags',
      component: () => import('@/views/admin/Tags.vue'),
      meta: {
        requiresAuth: true,
        title: '标签管理',
        icon: 'PriceTag',
        permissions: [TAG_PERMISSIONS.LIST, TAG_PERMISSIONS.READ],
        roles: [ROLES.SUPER_ADMIN, ROLES.ADMIN, ROLES.EDITOR]
      }
    },
    {
      path: '/admin/comments',
      name: 'AdminComments',
      component: () => import('@/views/admin/Comments.vue'),
      meta: {
        requiresAuth: true,
        title: '评论管理',
        icon: 'ChatDotRound',
        permissions: [COMMENT_PERMISSIONS.LIST, COMMENT_PERMISSIONS.READ],
        roles: [ROLES.SUPER_ADMIN, ROLES.ADMIN, ROLES.EDITOR]
      }
    },
    {
      path: '/admin/posts',
      name: 'AdminPosts',
      component: () => import('@/views/admin/Posts.vue'),
      meta: {
        requiresAuth: true,
        title: '说说管理',
        icon: 'ChatLineRound',
        permissions: [POST_PERMISSIONS.LIST, POST_PERMISSIONS.READ],
        roles: [ROLES.SUPER_ADMIN, ROLES.ADMIN, ROLES.EDITOR]
      }
    },
    {
      path: '/admin/media',
      name: 'AdminMedia',
      component: () => import('@/views/admin/MediaManagement.vue'),
      meta: {
        requiresAuth: true,
        title: '媒体管理',
        icon: 'Picture',
        permissions: [MEDIA_PERMISSIONS.LIST, MEDIA_PERMISSIONS.READ],
        roles: [ROLES.SUPER_ADMIN, ROLES.ADMIN, ROLES.EDITOR]
      }
    },
    {
      path: '/admin/users',
      name: 'AdminUsers',
      component: () => import('@/views/admin/UserManagement.vue'),
      meta: {
        requiresAuth: true,
        title: '用户管理',
        icon: 'User',
        permissions: [USER_PERMISSIONS.LIST, USER_PERMISSIONS.READ],
        roles: [ROLES.SUPER_ADMIN, ROLES.ADMIN]
      }
    },
    {
      path: '/admin/roles',
      name: 'AdminRoles',
      component: () => import('@/views/admin/RoleManagement.vue'),
      meta: {
        requiresAuth: true,
        title: '角色管理',
        icon: 'Avatar',
        permissions: [ROLE_PERMISSIONS.LIST, ROLE_PERMISSIONS.READ],
        roles: [ROLES.SUPER_ADMIN, ROLES.ADMIN]
      }
    },
    {
      path: '/admin/user-roles',
      name: 'AdminUserRoles',
      component: () => import('@/views/admin/UserRoleAssignment.vue'),
      meta: {
        requiresAuth: true,
        title: '用户角色分配',
        icon: 'UserFilled',
        permissions: [ROLE_PERMISSIONS.ASSIGN],
        roles: [ROLES.SUPER_ADMIN, ROLES.ADMIN],
        hidden: true // 作为角色管理的子功能，不单独显示
      }
    },
    {
      path: '/admin/notifications',
      name: 'AdminNotifications',
      component: () => import('@/views/admin/NotificationCenter.vue'),
      meta: {
        requiresAuth: true,
        title: '通知中心',
        icon: 'Bell',
        permissions: [NOTIFICATION_PERMISSIONS.READ],
        roles: [ROLES.SUPER_ADMIN, ROLES.ADMIN, ROLES.EDITOR]
      }
    },
    {
      path: '/admin/audit-logs',
      name: 'AdminAuditLogs',
      component: () => import('@/views/admin/AuditLogManagement.vue'),
      meta: {
        requiresAuth: true,
        title: '审计日志',
        icon: 'Document',
        permissions: [AUDIT_LOG_PERMISSIONS.READ, AUDIT_LOG_PERMISSIONS.LIST],
        roles: [ROLES.SUPER_ADMIN, ROLES.ADMIN]
      }
    },
    {
      path: '/settings',
      name: 'Settings',
      component: () => import('@/views/Settings.vue'),
      meta: {
        requiresAuth: true,
        title: '系统设置',
        icon: 'Tools',
        permissions: [SYSTEM_PERMISSIONS.SETTINGS],
        roles: [ROLES.SUPER_ADMIN, ROLES.ADMIN]
      }
    },
    {
      path: '/my/audit-logs',
      name: 'MyAuditLogs',
      component: () => import('@/views/user/MyAuditLogs.vue'),
      meta: {
        requiresAuth: true,
        title: '我的操作日志',
        icon: 'List',
        hidden: true // 个人页面，不在管理菜单中显示
      }
    },
    {
      path: '/:pathMatch(.*)*',
      name: 'NotFound',
      component: () => import('@/views/error/NotFound.vue')
    }
  ]
})

// Navigation guard for authentication and permissions
router.beforeEach(async (to, _from, next) => {
  const authStore = useAuthStore()
  const uiStore = useUIStore()
  const rbacStore = useRbacStore()

  try {
    // 检查是否需要认证
    if (to.meta.requiresAuth && !authStore.isAuthenticated) {
      next('/login')
      return
    }

    // 如果用户已认证，进行权限和角色检查
    if (authStore.user && to.meta.requiresAuth) {
      const userId = authStore.user.id
      let hasAccess = true
      let errorMessage = '您没有访问此页面的权限'

      // 检查角色权限
      if (to.meta.roles && to.meta.roles.length > 0) {
        const userRoles = await rbacStore.getUserRolesByUserId(userId)
        const userRoleNames = userRoles.map(role => role.name)

        // 检查用户是否拥有所需角色之一
        const hasRequiredRole = to.meta.roles.some(role => userRoleNames.includes(role))

        if (!hasRequiredRole) {
          hasAccess = false
          errorMessage = `需要以下角色之一: ${to.meta.roles.join(', ')}`
        }
      }

      // 检查具体权限（只有在角色检查通过或没有角色要求时才检查）
      if (hasAccess && to.meta.permissions && to.meta.permissions.length > 0) {
        const permissions = Array.isArray(to.meta.permissions)
          ? to.meta.permissions
          : [to.meta.permissions]

        // 使用批量检查优化性能
        let hasAnyPermission = false
        if (permissions.length > 1) {
          const permissionResults = await rbacStore.checkUserPermissions(userId, permissions)
          hasAnyPermission = Object.values(permissionResults).some(Boolean)
        } else {
          hasAnyPermission = await rbacStore.checkUserPermission(userId, permissions[0])
        }

        // 如果用户没有任何所需权限，拒绝访问
        if (!hasAnyPermission) {
          hasAccess = false
          errorMessage = `缺少以下权限之一: ${permissions.join(', ')}`
        }
      }

      // 如果没有访问权限，拒绝访问
      if (!hasAccess) {
        uiStore.showError(errorMessage)
        // 根据用户角色重定向到合适的页面
        const userRoles = await rbacStore.getUserRolesByUserId(userId)
        const userRoleNames = userRoles.map(role => role.name)

        if (userRoleNames.includes(ROLES.SUPER_ADMIN) ||
          userRoleNames.includes(ROLES.ADMIN) ||
          userRoleNames.includes(ROLES.EDITOR)) {
          next('/admin')
        } else {
          next('/')
        }
        return
      }
    }

    next()
  } catch (error) {
    console.error('Navigation error:', error)
    uiStore.showError('页面导航失败，请重试')
    next(false)
  }
})

// Global error handler for route loading errors
router.onError((error) => {
  const uiStore = useUIStore()
  console.error('Router error:', error)
  uiStore.showError('页面加载失败，请刷新页面重试')
})

export default router