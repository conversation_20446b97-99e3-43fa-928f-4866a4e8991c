{"version": 3, "file": "permissionValidator.js", "sourceRoot": "", "sources": ["../../src/middleware/permissionValidator.ts"], "names": [], "mappings": ";;;AAOA,iDAA4C;AAC5C,iDAA6C;AAC7C,yCAAqC;AAmB9B,MAAM,mBAAmB,GAAG,CAAC,MAAkC,EAAE,EAAE;IACxE,OAAO,KAAK,EAAE,GAAyB,EAAE,GAAa,EAAE,IAAkB,EAAiB,EAAE;QAC3F,IAAI,CAAC;YAEH,IAAI,CAAC,GAAG,CAAC,IAAI,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,EAAE,CAAC;gBAC9B,MAAM,IAAA,0BAAW,EAAC,GAAG,EAAE,aAAa,EAAE,yBAAyB,CAAC,CAAA;YAClE,CAAC;YAED,MAAM,MAAM,GAAG,GAAG,CAAC,IAAI,CAAC,EAAE,CAAA;YAC1B,MAAM,EAAE,WAAW,EAAE,KAAK,EAAE,UAAU,GAAG,KAAK,EAAE,SAAS,GAAG,KAAK,EAAE,kBAAkB,EAAE,WAAW,GAAG,KAAK,EAAE,GAAG,MAAM,CAAA;YAGrH,IAAI,SAAS,IAAI,WAAW,EAAE,CAAC;gBAC7B,MAAM,OAAO,GAAG,MAAM,sBAAsB,CAAC,GAAG,EAAE,MAAM,EAAE,kBAAkB,CAAC,CAAA;gBAE7E,IAAI,OAAO,EAAE,CAAC;oBACZ,IAAI,WAAW,EAAE,CAAC;wBAChB,OAAO,IAAI,EAAE,CAAA;oBACf,CAAC;oBACD,IAAI,SAAS,IAAI,CAAC,WAAW,IAAI,CAAC,KAAK,EAAE,CAAC;wBACxC,OAAO,IAAI,EAAE,CAAA;oBACf,CAAC;gBACH,CAAC;YACH,CAAC;YAGD,MAAM,IAAI,GAAG,MAAM,WAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAA;YACxC,IAAI,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC;gBAC5B,MAAM,IAAA,0BAAW,EAAC,GAAG,EAAE,UAAU,EAAE,eAAe,CAAC,CAAA;YACrD,CAAC;YAGD,IAAI,KAAK,IAAI,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBAC9B,MAAM,gBAAgB,GAAG,MAAM,cAAc,CAAC,MAAM,EAAE,KAAK,EAAE,UAAU,CAAC,CAAA;gBACxE,IAAI,CAAC,gBAAgB,EAAE,CAAC;oBACtB,MAAM,IAAA,0BAAW,EAAC,GAAG,EAAE,SAAS,UAAU,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,KAAK,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,EAAE,oBAAoB,CAAC,CAAA;gBACxG,CAAC;YACH,CAAC;YAGD,IAAI,WAAW,IAAI,WAAW,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBAC1C,MAAM,sBAAsB,GAAG,MAAM,oBAAoB,CAAC,MAAM,EAAE,WAAW,EAAE,UAAU,CAAC,CAAA;gBAC1F,IAAI,CAAC,sBAAsB,EAAE,CAAC;oBAC5B,MAAM,IAAA,0BAAW,EAAC,GAAG,EAAE,SAAS,UAAU,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,KAAK,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,EAAE,0BAA0B,CAAC,CAAA;gBACpH,CAAC;YACH,CAAC;YAGD,IAAI,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,aAAa,EAAE,CAAC;gBAC3C,OAAO,CAAC,GAAG,CAAC,gBAAgB,MAAM,SAAS,WAAW,EAAE,IAAI,CAAC,GAAG,CAAC,IAAI,KAAK,SAAS,KAAK,EAAE,IAAI,CAAC,GAAG,CAAC,IAAI,KAAK,EAAE,CAAC,CAAA;YACjH,CAAC;YAED,IAAI,EAAE,CAAA;QACR,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,KAAK,CAAC,CAAA;QACb,CAAC;IACH,CAAC,CAAA;AACH,CAAC,CAAA;AAzDY,QAAA,mBAAmB,uBAyD/B;AASD,KAAK,UAAU,cAAc,CAAC,MAAc,EAAE,aAAuB,EAAE,UAAmB;IACxF,IAAI,CAAC;QACH,MAAM,SAAS,GAAG,MAAM,mBAAQ,CAAC,YAAY,CAAC,MAAM,CAAC,CAAA;QACrD,MAAM,aAAa,GAAG,SAAS,CAAC,GAAG,CAAC,CAAC,IAAS,EAAE,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;QAE7D,IAAI,UAAU,EAAE,CAAC;YACf,OAAO,aAAa,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC,aAAa,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAA;QAClE,CAAC;aAAM,CAAC;YACN,OAAO,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,aAAa,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAA;QACjE,CAAC;IACH,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,OAAO,CAAC,KAAK,CAAC,WAAW,EAAE,KAAK,CAAC,CAAA;QACjC,OAAO,KAAK,CAAA;IACd,CAAC;AACH,CAAC;AASD,KAAK,UAAU,oBAAoB,CAAC,MAAc,EAAE,mBAA6B,EAAE,UAAmB;IACpG,IAAI,CAAC;QACH,IAAI,UAAU,EAAE,CAAC;YAEf,KAAK,MAAM,UAAU,IAAI,mBAAmB,EAAE,CAAC;gBAC7C,MAAM,aAAa,GAAG,MAAM,mBAAQ,CAAC,aAAa,CAAC,MAAM,EAAE,UAAU,CAAC,CAAA;gBACtE,IAAI,CAAC,aAAa,EAAE,CAAC;oBACnB,OAAO,KAAK,CAAA;gBACd,CAAC;YACH,CAAC;YACD,OAAO,IAAI,CAAA;QACb,CAAC;aAAM,CAAC;YAEN,KAAK,MAAM,UAAU,IAAI,mBAAmB,EAAE,CAAC;gBAC7C,MAAM,aAAa,GAAG,MAAM,mBAAQ,CAAC,aAAa,CAAC,MAAM,EAAE,UAAU,CAAC,CAAA;gBACtE,IAAI,aAAa,EAAE,CAAC;oBAClB,OAAO,IAAI,CAAA;gBACb,CAAC;YACH,CAAC;YACD,OAAO,KAAK,CAAA;QACd,CAAC;IACH,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,OAAO,CAAC,KAAK,CAAC,WAAW,EAAE,KAAK,CAAC,CAAA;QACjC,OAAO,KAAK,CAAA;IACd,CAAC;AACH,CAAC;AASD,KAAK,UAAU,sBAAsB,CAAC,GAAyB,EAAE,MAAc,EAAE,UAAmB;IAClG,IAAI,CAAC;QAEH,IAAI,CAAC,UAAU,EAAE,CAAC;YAChB,MAAM,cAAc,GAAG,QAAQ,CAAC,GAAG,CAAC,MAAM,CAAC,MAAM,IAAI,GAAG,CAAC,MAAM,CAAC,EAAE,IAAI,GAAG,CAAC,CAAA;YAC1E,OAAO,cAAc,KAAK,MAAM,CAAA;QAClC,CAAC;QAKD,OAAO,KAAK,CAAA;IACd,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,OAAO,CAAC,KAAK,CAAC,YAAY,EAAE,KAAK,CAAC,CAAA;QAClC,OAAO,KAAK,CAAA;IACd,CAAC;AACH,CAAC;AAMY,QAAA,oBAAoB,GAAG;IAIlC,YAAY,EAAE,GAAG,EAAE,CAAC,IAAA,2BAAmB,EAAC;QACtC,KAAK,EAAE,CAAC,aAAa,EAAE,OAAO,CAAC;KAChC,CAAC;IAKF,aAAa,EAAE,GAAG,EAAE,CAAC,IAAA,2BAAmB,EAAC;QACvC,KAAK,EAAE,CAAC,aAAa,EAAE,OAAO,EAAE,QAAQ,CAAC;KAC1C,CAAC;IAKF,iBAAiB,EAAE,CAAC,UAAkB,EAAE,EAAE,CAAC,IAAA,2BAAmB,EAAC;QAC7D,WAAW,EAAE,CAAC,UAAU,CAAC;KAC1B,CAAC;IAKF,oBAAoB,EAAE,CAAC,WAAqB,EAAE,EAAE,CAAC,IAAA,2BAAmB,EAAC;QACnE,WAAW;QACX,UAAU,EAAE,KAAK;KAClB,CAAC;IAKF,qBAAqB,EAAE,CAAC,WAAqB,EAAE,EAAE,CAAC,IAAA,2BAAmB,EAAC;QACpE,WAAW;QACX,UAAU,EAAE,IAAI;KACjB,CAAC;IAKF,4BAA4B,EAAE,CAAC,UAAkB,EAAE,EAAE,CAAC,IAAA,2BAAmB,EAAC;QACxE,WAAW,EAAE,CAAC,UAAU,CAAC;QACzB,SAAS,EAAE,IAAI;QACf,WAAW,EAAE,IAAI;KAClB,CAAC;IAKF,sBAAsB,EAAE,CAAC,KAAe,EAAE,EAAE,CAAC,IAAA,2BAAmB,EAAC;QAC/D,KAAK;QACL,SAAS,EAAE,IAAI;QACf,WAAW,EAAE,IAAI;KAClB,CAAC;CACH,CAAA;AAMD,MAAM,eAAe;IAArB;QACU,UAAK,GAAG,IAAI,GAAG,EAAkD,CAAA;QACxD,QAAG,GAAG,CAAC,GAAG,EAAE,GAAG,IAAI,CAAA;IA4CtC,CAAC;IAvCC,GAAG,CAAC,GAAW;QACb,MAAM,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,GAAG,CAAC,CAAA;QAClC,IAAI,CAAC,MAAM;YAAE,OAAO,IAAI,CAAA;QAExB,IAAI,IAAI,CAAC,GAAG,EAAE,GAAG,MAAM,CAAC,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;YAC7C,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,GAAG,CAAC,CAAA;YACtB,OAAO,IAAI,CAAA;QACb,CAAC;QAED,OAAO,MAAM,CAAC,MAAM,CAAA;IACtB,CAAC;IAKD,GAAG,CAAC,GAAW,EAAE,MAAe;QAC9B,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,GAAG,EAAE;YAClB,MAAM;YACN,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;SACtB,CAAC,CAAA;IACJ,CAAC;IAKD,KAAK;QACH,IAAI,CAAC,KAAK,CAAC,KAAK,EAAE,CAAA;IACpB,CAAC;IAKD,cAAc,CAAC,MAAc;QAC3B,KAAK,MAAM,CAAC,GAAG,CAAC,IAAI,IAAI,CAAC,KAAK,EAAE,CAAC;YAC/B,IAAI,GAAG,CAAC,UAAU,CAAC,QAAQ,MAAM,GAAG,CAAC,EAAE,CAAC;gBACtC,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,GAAG,CAAC,CAAA;YACxB,CAAC;QACH,CAAC;IACH,CAAC;CACF;AAEY,QAAA,eAAe,GAAG,IAAI,eAAe,EAAE,CAAA;AAO7C,MAAM,4BAA4B,GAAG,CAAC,MAAkC,EAAE,EAAE;IACjF,OAAO,KAAK,EAAE,GAAyB,EAAE,GAAa,EAAE,IAAkB,EAAiB,EAAE;QAC3F,IAAI,CAAC;YACH,IAAI,CAAC,GAAG,CAAC,IAAI,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,EAAE,CAAC;gBAC9B,MAAM,IAAA,0BAAW,EAAC,GAAG,EAAE,aAAa,EAAE,yBAAyB,CAAC,CAAA;YAClE,CAAC;YAED,MAAM,MAAM,GAAG,GAAG,CAAC,IAAI,CAAC,EAAE,CAAA;YAC1B,MAAM,QAAQ,GAAG,QAAQ,MAAM,IAAI,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,EAAE,CAAA;YAG3D,MAAM,YAAY,GAAG,uBAAe,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAA;YAClD,IAAI,YAAY,KAAK,IAAI,EAAE,CAAC;gBAC1B,IAAI,YAAY,EAAE,CAAC;oBACjB,OAAO,IAAI,EAAE,CAAA;gBACf,CAAC;qBAAM,CAAC;oBACN,MAAM,IAAA,0BAAW,EAAC,GAAG,EAAE,MAAM,EAAE,0BAA0B,CAAC,CAAA;gBAC5D,CAAC;YACH,CAAC;YAGD,MAAM,YAAY,GAAG,IAAI,CAAA;YACzB,IAAI,aAAa,GAAG,KAAK,CAAA;YAEzB,MAAM,QAAQ,GAAG,CAAC,KAAW,EAAE,EAAE;gBAC/B,IAAI,CAAC,KAAK,EAAE,CAAC;oBACX,aAAa,GAAG,IAAI,CAAA;oBACpB,uBAAe,CAAC,GAAG,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAA;oBACnC,YAAY,EAAE,CAAA;gBAChB,CAAC;qBAAM,CAAC;oBACN,uBAAe,CAAC,GAAG,CAAC,QAAQ,EAAE,KAAK,CAAC,CAAA;oBACpC,YAAY,CAAC,KAAK,CAAC,CAAA;gBACrB,CAAC;YACH,CAAC,CAAA;YAED,MAAM,IAAA,2BAAmB,EAAC,MAAM,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,QAAQ,CAAC,CAAA;QACvD,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,KAAK,CAAC,CAAA;QACb,CAAC;IACH,CAAC,CAAA;AACH,CAAC,CAAA;AAxCY,QAAA,4BAA4B,gCAwCxC"}